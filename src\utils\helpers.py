"""
Helper functions for MignalyBot
"""

import os
import logging
import httpx
from datetime import datetime, timedelta
import pytz

logger = logging.getLogger(__name__)

# Global timezone cache to avoid repeated lookups
_timezone_cache = None

def get_timezone():
    """
    Get the timezone from environment variable or default to Tehran timezone
    Uses caching for performance.

    Returns:
        datetime.tzinfo: Timezone object
    """
    global _timezone_cache

    if _timezone_cache is not None:
        return _timezone_cache

    timezone_str = os.getenv("TIMEZONE", "Asia/Tehran")
    try:
        _timezone_cache = pytz.timezone(timezone_str)
        logger.debug(f"Using timezone: {timezone_str} ({_timezone_cache})")
        return _timezone_cache
    except pytz.exceptions.UnknownTimeZoneError:
        logger.warning(f"Unknown timezone: {timezone_str}, using Asia/Tehran")
        _timezone_cache = pytz.timezone('Asia/Tehran')
        return _timezone_cache

def get_current_time():
    """
    Get the current time in the configured timezone

    Returns:
        datetime.datetime: Current time with timezone info
    """
    tz = get_timezone()
    return datetime.now(tz)

def get_utc_time():
    """
    Get the current time in UTC

    Returns:
        datetime.datetime: Current UTC time with timezone info
    """
    return datetime.now(pytz.UTC)

def to_utc(dt):
    """
    Convert a datetime to UTC

    Args:
        dt (datetime.datetime): Datetime to convert

    Returns:
        datetime.datetime: UTC datetime with timezone info
    """
    if dt is None:
        return None

    if dt.tzinfo is None:
        # Assume naive datetime is in the configured timezone
        tz = get_timezone()
        dt = tz.localize(dt)

    return dt.astimezone(pytz.UTC)

def to_local(dt):
    """
    Convert a datetime to the configured local timezone

    Args:
        dt (datetime.datetime): Datetime to convert (assumed UTC if naive)

    Returns:
        datetime.datetime: Local datetime with timezone info
    """
    if dt is None:
        return None

    if dt.tzinfo is None:
        # Assume naive datetime is UTC
        dt = pytz.UTC.localize(dt)

    tz = get_timezone()
    return dt.astimezone(tz)

def parse_datetime_from_frontend(datetime_str):
    """
    Parse datetime string from frontend (admin interface)
    Frontend sends datetime in format: YYYY-MM-DDTHH:MM
    This is interpreted as being in the server's configured timezone

    Args:
        datetime_str (str): Datetime string from frontend

    Returns:
        datetime.datetime: UTC datetime with timezone info
    """
    if not datetime_str:
        return None

    try:
        # Parse the datetime string (no timezone info)
        dt = datetime.strptime(datetime_str, "%Y-%m-%dT%H:%M")

        # Assume it's in the configured timezone
        tz = get_timezone()
        dt_local = tz.localize(dt)

        # Convert to UTC for storage
        return dt_local.astimezone(pytz.UTC)
    except ValueError as e:
        logger.error(f"Error parsing datetime from frontend: {datetime_str} - {e}")
        return None

def format_datetime_for_frontend(dt):
    """
    Format datetime for frontend display
    Converts UTC datetime to local timezone and formats for display

    Args:
        dt (datetime.datetime): UTC datetime

    Returns:
        str: Formatted datetime string for frontend
    """
    if dt is None:
        return None

    # Convert to local timezone
    local_dt = to_local(dt)

    # Format for display
    return local_dt.strftime("%Y-%m-%d %H:%M:%S")

def format_datetime_with_timezone(dt):
    """
    Format datetime with timezone information for display

    Args:
        dt (datetime.datetime): Datetime to format

    Returns:
        str: Formatted datetime string with timezone
    """
    if dt is None:
        return None

    # Convert to local timezone if needed
    if dt.tzinfo is None:
        dt = pytz.UTC.localize(dt)

    local_dt = to_local(dt)
    tz = get_timezone()

    return f"{local_dt.strftime('%Y-%m-%d %H:%M:%S')} ({tz.zone})"

def to_local_timezone(dt):
    """
    Convert a datetime to the configured local timezone

    Args:
        dt (datetime.datetime): Datetime to convert

    Returns:
        datetime.datetime: Local timezone datetime with timezone info
    """
    if dt is None:
        return None

    if dt.tzinfo is None:
        # Assume naive datetime is in UTC
        dt = pytz.UTC.localize(dt)

    tz = get_timezone()
    return dt.astimezone(tz)

def format_datetime(dt, format_str="%Y-%m-%d %H:%M:%S"):
    """
    Format a datetime object as a string in the configured timezone

    Args:
        dt (datetime.datetime): Datetime to format
        format_str (str, optional): Format string

    Returns:
        str: Formatted datetime string
    """
    if not dt:
        return ""

    # Convert to configured timezone using the new utility
    local_dt = to_local_timezone(dt)
    return local_dt.strftime(format_str)

def format_datetime_with_timezone(dt, format_str="%Y-%m-%d %H:%M:%S %Z"):
    """
    Format a datetime object as a string in the configured timezone with timezone info

    Args:
        dt (datetime.datetime): Datetime to format
        format_str (str, optional): Format string

    Returns:
        str: Formatted datetime string with timezone
    """
    if not dt:
        return ""

    # Convert to configured timezone using the new utility
    local_dt = to_local_timezone(dt)
    return local_dt.strftime(format_str)

def parse_datetime_from_frontend(dt_str):
    """
    Parse a datetime string from the frontend (admin interface)
    Frontend sends datetime in the format: "YYYY-MM-DDTHH:MM"
    We interpret this as being in the configured timezone

    Args:
        dt_str (str): Datetime string from frontend

    Returns:
        datetime.datetime: Timezone-aware datetime in UTC for database storage
    """
    if not dt_str:
        return None

    try:
        # Parse the ISO format datetime string
        if dt_str.endswith('Z'):
            dt_str = dt_str.replace('Z', '+00:00')

        if 'T' in dt_str and len(dt_str) == 16:  # Format: "YYYY-MM-DDTHH:MM"
            naive_time = datetime.strptime(dt_str, "%Y-%m-%dT%H:%M")
        else:
            naive_time = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))

        # If naive, assume it's in the configured timezone
        if naive_time.tzinfo is None:
            tz = get_timezone()
            local_time = tz.localize(naive_time)
        else:
            local_time = naive_time

        # Convert to UTC for database storage
        return to_utc(local_time)

    except (ValueError, TypeError) as e:
        logger.error(f"Error parsing datetime from frontend: {dt_str}, error: {e}")
        return None

def parse_datetime(dt_str, format_str="%Y-%m-%d %H:%M:%S"):
    """
    Parse a datetime string

    Args:
        dt_str (str): Datetime string to parse
        format_str (str, optional): Format string

    Returns:
        datetime.datetime: Parsed datetime object
    """
    if not dt_str:
        return None

    try:
        dt = datetime.strptime(dt_str, format_str)
        # Set timezone
        timezone = get_timezone()
        dt = timezone.localize(dt)
        return dt
    except ValueError:
        logger.error(f"Error parsing datetime: {dt_str}")
        return None

def is_forex_pair(symbol):
    """Check if symbol is a forex pair (excluding indices/commodities that don't use pips)"""
    # Normalize symbol by removing slashes and converting to uppercase
    normalized_symbol = symbol.upper().replace('/', '')

    base_forex_pairs = [
        # Major forex pairs
        'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
        # Cross pairs
        'EURJPY', 'GBPJPY', 'EURGBP', 'EURAUD', 'EURCHF', 'AUDCHF', 'GBPCHF',
        'CADCHF', 'NZDCHF', 'NZDJPY', 'AUDCAD', 'AUDNZD', 'CADJPY', 'CHFJPY',
        'EURNZD', 'EURCAD', 'GBPAUD', 'GBPCAD', 'GBPNZD', 'NZDCAD'
        # Note: DJIUSD and XAUUSD excluded - they don't use pip calculations
    ]
    # Add both original and slash format
    forex_pairs = base_forex_pairs + [f"{p[:3]}/{p[3:]}" for p in base_forex_pairs]
    return any(pair == normalized_symbol for pair in forex_pairs)

def format_number(number, decimal_places=2):
    """
    Format a number with the specified number of decimal places

    Args:
        number (float): Number to format
        decimal_places (int, optional): Number of decimal places

    Returns:
        str: Formatted number string
    """
    if number is None:
        return ""

    try:
        return f"{number:.{decimal_places}f}"
    except (ValueError, TypeError):
        logger.error(f"Error formatting number: {number}")
        return str(number)

def format_trading_price(price, symbol=None, max_digits=4):
    """
    Format trading prices with appropriate precision
    Forex pairs use 5 decimal places, others use the original logic

    Args:
        price (float): Price to format
        symbol (str, optional): Trading symbol to determine if it's forex
        max_digits (int): Maximum total digits (default 4, ignored for forex)

    Returns:
        str: Formatted price string
    """
    if price is None:
        return "N/A"

    try:
        price = float(price)

        # Check if this is a forex pair - if so, use 5 decimal places
        if symbol and is_forex_pair(symbol):
            return f"{price:.5f}"

        # Handle very small numbers (crypto prices like 0.0001234)
        if price < 0.001:
            # For very small numbers, use scientific notation or limit to 4 significant digits
            formatted = f"{price:.3e}"
            return formatted

        # Handle numbers less than 1
        elif price < 1:
            # For prices like 0.1234, 0.5678
            return f"{price:.3f}"

        # Handle numbers 1-9999
        elif price < 10000:
            if price >= 1000:
                # For prices like 1234, 5678 - no decimals
                return f"{price:.0f}"
            elif price >= 100:
                # For prices like 123.4, 567.8 - 1 decimal
                return f"{price:.1f}"
            elif price >= 10:
                # For prices like 12.34, 56.78 - 2 decimals
                return f"{price:.2f}"
            else:
                # For prices like 1.234, 5.678 - 3 decimals
                return f"{price:.3f}"

        # Handle large numbers (10000+)
        else:
            # For large numbers, use K notation
            if price >= 1000000:
                return f"{price/1000000:.1f}M"
            elif price >= 1000:
                return f"{price/1000:.1f}K"
            else:
                return f"{price:.0f}"

    except (ValueError, TypeError):
        logger.error(f"Error formatting trading price: {price}")
        return str(price)[:max_digits]

def format_percentage(number, decimal_places=2):
    """
    Format a number as a percentage

    Args:
        number (float): Number to format
        decimal_places (int, optional): Number of decimal places

    Returns:
        str: Formatted percentage string
    """
    if number is None:
        return ""

    try:
        return f"{number:.{decimal_places}f}%"
    except (ValueError, TypeError):
        logger.error(f"Error formatting percentage: {number}")
        return str(number)

def truncate_text(text, max_length=100, suffix="..."):
    """
    Truncate text to the specified length

    Args:
        text (str): Text to truncate
        max_length (int, optional): Maximum length
        suffix (str, optional): Suffix to add if truncated

    Returns:
        str: Truncated text
    """
    if not text:
        return ""

    if len(text) <= max_length:
        return text

    return text[:max_length - len(suffix)] + suffix

async def download_file(url, save_path):
    """
    Download a file from a URL

    Args:
        url (str): URL to download from
        save_path (str): Path to save the file to

    Returns:
        bool: True if download was successful, False otherwise
    """
    if not url:
        return False

    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # Set headers to mimic a browser request
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "*/*",
            "Accept-Language": "en-US,en;q=0.9",
            "Referer": "https://www.google.com/"
        }

        # Download the file
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers, follow_redirects=True)

            if response.status_code != 200:
                logger.error(f"Failed to download file: {response.status_code}")
                return False

            # Save the file
            with open(save_path, "wb") as f:
                f.write(response.content)

            return True

    except Exception as e:
        logger.error(f"Error downloading file: {e}", exc_info=True)
        return False

def get_relative_time_str(dt):
    """
    Get a relative time string (e.g., "2 hours ago")

    Args:
        dt (datetime.datetime): Datetime to get relative time for

    Returns:
        str: Relative time string
    """
    if not dt:
        return ""

    # Convert to timezone
    timezone = get_timezone()
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=pytz.UTC)
    dt = dt.astimezone(timezone)

    # Ensure both datetimes are timezone-aware for comparison
    now = datetime.now(timezone)

    # Convert both to UTC for consistent comparison to avoid timezone issues
    dt_utc = dt.astimezone(pytz.UTC)
    now_utc = now.astimezone(pytz.UTC)
    diff = now_utc - dt_utc

    if diff.days < 0:
        # Future date
        if diff.days > -1:
            # Less than a day in the future
            hours = -diff.seconds // 3600
            if hours < 1:
                minutes = -diff.seconds // 60
                return f"in {minutes} minutes"
            return f"in {hours} hours"
        elif diff.days > -7:
            # Less than a week in the future
            return f"in {-diff.days} days"
        elif diff.days > -30:
            # Less than a month in the future
            weeks = -diff.days // 7
            return f"in {weeks} weeks"
        elif diff.days > -365:
            # Less than a year in the future
            months = -diff.days // 30
            return f"in {months} months"
        else:
            # More than a year in the future
            years = -diff.days // 365
            return f"in {years} years"

    if diff.days == 0:
        # Today
        if diff.seconds < 60:
            return "just now"
        elif diff.seconds < 3600:
            minutes = diff.seconds // 60
            return f"{minutes} minutes ago"
        else:
            hours = diff.seconds // 3600
            return f"{hours} hours ago"
    elif diff.days == 1:
        return "yesterday"
    elif diff.days < 7:
        return f"{diff.days} days ago"
    elif diff.days < 30:
        weeks = diff.days // 7
        return f"{weeks} weeks ago"
    elif diff.days < 365:
        months = diff.days // 30
        return f"{months} months ago"
    else:
        years = diff.days // 365
        return f"{years} years ago"
