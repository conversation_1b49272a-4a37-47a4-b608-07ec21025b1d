{% extends "base.html" %}

{% block title %}Configuration - MignalyBot Admin{% endblock %}

{% block page_title %}Configuration{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Global Configuration</h5>
    </div>
    <div class="card-body">
        <form id="configForm">
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">API Settings</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="qwenApiKey" class="form-label">Qwen API Key</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="qwenApiKey" name="qwen_api_key" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleApiKey">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">Your Qwen API key for AI content generation</div>
                            </div>
                            <div class="mb-3">
                                <label for="maxTokens" class="form-label">Max Tokens Per Request</label>
                                <input type="number" class="form-control" id="maxTokens" name="max_tokens_per_request" min="100" max="8000" required>
                                <div class="form-text">Maximum tokens to use per API request</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Content Settings</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="defaultLanguage" class="form-label">Default Language</label>
                                <select class="form-select" id="defaultLanguage" name="default_language" required>
                                    <option value="en">English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                    <option value="de">German</option>
                                    <option value="it">Italian</option>
                                    <option value="pt">Portuguese</option>
                                    <option value="ru">Russian</option>
                                    <option value="zh">Chinese</option>
                                    <option value="ja">Japanese</option>
                                    <option value="ko">Korean</option>
                                    <option value="ar">Arabic</option>
                                    <option value="fa">Farsi (Persian)</option>
                                    <option value="hi">Hindi</option>
                                    <option value="tr">Turkish</option>
                                </select>
                                <div class="form-text">Default language for content generation</div>
                            </div>
                            <div class="mb-3">
                                <label for="postFrequency" class="form-label">Post Frequency (hours)</label>
                                <input type="number" class="form-control" id="postFrequency" name="post_frequency" min="1" max="24" required>
                                <div class="form-text">How often to collect data and generate content</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Market Data Settings</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="symbols" class="form-label">Symbols</label>
                                <input type="text" class="form-control" id="symbols" name="symbols" required>
                                <div class="form-text">Comma-separated list of symbols to track (e.g., BTC/USD,ETH/USD,EUR/USD)</div>
                            </div>
                            <div class="mb-3">
                                <label for="timeframes" class="form-label">Timeframes</label>
                                <input type="text" class="form-control" id="timeframes" name="timeframes" required>
                                <div class="form-text">Comma-separated list of timeframes to analyze (e.g., 1h,4h,1d)</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Feature Settings</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="enableNews" name="enable_news">
                                <label class="form-check-label" for="enableNews">Enable News Collection</label>
                                <div class="form-text">Collect and analyze financial news</div>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="enableSignals" name="enable_signals">
                                <label class="form-check-label" for="enableSignals">Enable Trading Signals</label>
                                <div class="form-text">Generate and track trading signals</div>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="enableCalendar" name="enable_calendar">
                                <label class="form-check-label" for="enableCalendar">Enable Economic Calendar</label>
                                <div class="form-text">Collect and analyze economic events</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end">
                <button type="button" class="btn btn-secondary me-2" id="resetConfigBtn">Reset</button>
                <button type="submit" class="btn btn-primary">Save Configuration</button>
            </div>
        </form>
    </div>
</div>

<!-- Test API Modal -->
<div class="modal fade" id="testApiModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test API Connection</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="apiTestResult">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status"></div>
                        <div>Testing API connection...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Load configuration
        loadConfig();

        // Toggle API key visibility
        $('#toggleApiKey').click(function() {
            const apiKeyInput = $('#qwenApiKey');
            const icon = $(this).find('i');

            if (apiKeyInput.attr('type') === 'password') {
                apiKeyInput.attr('type', 'text');
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                apiKeyInput.attr('type', 'password');
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });

        // Reset config form
        $('#resetConfigBtn').click(function() {
            loadConfig();
        });

        // Submit config form
        $('#configForm').submit(function(e) {
            e.preventDefault();

            const formData = {
                qwen_api_key: $('#qwenApiKey').val(),
                default_language: $('#defaultLanguage').val(),
                post_frequency: parseInt($('#postFrequency').val()),
                symbols: $('#symbols').val(),
                timeframes: $('#timeframes').val(),
                enable_news: $('#enableNews').is(':checked'),
                enable_signals: $('#enableSignals').is(':checked'),
                enable_calendar: $('#enableCalendar').is(':checked'),
                max_tokens_per_request: parseInt($('#maxTokens').val())
            };

            $.ajax({
                url: '/api/config',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function(response) {
                    alert('Configuration saved successfully!');

                    // Show API test modal if API key was changed
                    if (formData.qwen_api_key !== initialApiKey && formData.qwen_api_key !== '') {
                        testApiConnection(formData.qwen_api_key);
                    }
                },
                error: function(xhr) {
                    alert('Error saving configuration: ' + xhr.responseJSON.detail);
                }
            });
        });
    });

    let initialApiKey = '';

    function loadConfig() {
        $.ajax({
            url: '/api/config',
            type: 'GET',
            success: function(data) {
                $('#qwenApiKey').val(data.qwen_api_key);
                initialApiKey = data.qwen_api_key;
                $('#defaultLanguage').val(data.default_language);
                $('#postFrequency').val(data.post_frequency);
                $('#symbols').val(data.symbols);
                $('#timeframes').val(data.timeframes);
                $('#enableNews').prop('checked', data.enable_news);
                $('#enableSignals').prop('checked', data.enable_signals);
                $('#enableCalendar').prop('checked', data.enable_calendar);
                $('#maxTokens').val(data.max_tokens_per_request);
            },
            error: function(xhr) {
                alert('Error loading configuration: ' + xhr.responseJSON.detail);
            }
        });
    }

    function testApiConnection(apiKey) {
        $('#testApiModal').modal('show');

        $.ajax({
            url: '/api/actions/test-qwen',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ prompt: 'Hello, please respond with a short greeting to confirm the API is working correctly.' }),
            success: function(response) {
                $('#apiTestResult').html(`
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle"></i> API Connection Successful!</h5>
                        <p>Response: ${response.response}</p>
                    </div>
                `);
            },
            error: function(xhr) {
                $('#apiTestResult').html(`
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-circle"></i> API Connection Failed!</h5>
                        <p>Error: ${xhr.responseJSON.detail}</p>
                        <p>Please check your API key and try again.</p>
                    </div>
                `);
            }
        });
    }
</script>
{% endblock %}
