{% extends "base.html" %}

{% block title %}Posts - MignalyBot Admin{% endblock %}

{% block page_title %}Posts Management{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Telegram Posts</h5>
        <button type="button" class="btn btn-primary" id="createPostBtn">
            <i class="fas fa-plus"></i> Create Post
        </button>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <div class="row">
                <div class="col-md-3">
                    <select class="form-select" id="statusFilter">
                        <option value="">All Statuses</option>
                        <option value="draft">Draft</option>
                        <option value="scheduled">Scheduled</option>
                        <option value="published">Published</option>
                        <option value="failed">Failed</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="typeFilter">
                        <option value="">All Types</option>
                        <option value="news">News</option>
                        <option value="signal">Signal</option>
                        <option value="analysis">Analysis</option>
                        <option value="event">Event</option>
                        <option value="performance">Performance</option>
                        <option value="greeting">Greeting</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="channelFilter">
                        <option value="">All Channels</option>
                        <!-- Channels will be loaded dynamically -->
                    </select>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-primary w-100" id="refreshPostsBtn">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover" id="postsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Channel</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Scheduled</th>
                        <th>Published</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                            <p>Loading posts...</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="d-flex justify-content-between align-items-center mt-3">
            <div>
                <span id="postsCount">0</span> posts found
            </div>
            <div>
                <nav aria-label="Posts pagination">
                    <ul class="pagination" id="postsPagination">
                        <!-- Pagination will be generated dynamically -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Post Modal -->
<div class="modal fade" id="postModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="postModalTitle">Create Post</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="postForm">
                    <input type="hidden" id="postId">

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="postChannel" class="form-label">Channel</label>
                                <select class="form-select" id="postChannel" name="channel_id" required>
                                    <option value="">Select Channel</option>
                                    <!-- Channels will be loaded dynamically -->
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="postType" class="form-label">Post Type</label>
                                <select class="form-select" id="postType" name="type" required>
                                    <option value="news">News</option>
                                    <option value="signal">Signal</option>
                                    <option value="analysis">Analysis</option>
                                    <option value="event">Event</option>
                                    <option value="performance">Performance</option>
                                    <option value="greeting">Greeting</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="postStatus" class="form-label">Status</label>
                                <select class="form-select" id="postStatus" name="status" required>
                                    <option value="draft">Draft</option>
                                    <option value="scheduled">Scheduled</option>
                                </select>
                            </div>

                            <div class="mb-3" id="scheduledTimeGroup">
                                <label for="postScheduledTime" class="form-label">Scheduled Time</label>
                                <input type="datetime-local" class="form-control" id="postScheduledTime" name="scheduled_time">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3" id="newsGroup">
                                <label for="postNews" class="form-label">News Item</label>
                                <select class="form-select" id="postNews" name="news_id">
                                    <option value="">Select News Item</option>
                                    <!-- News items will be loaded dynamically -->
                                </select>
                            </div>

                            <div class="mb-3" id="eventGroup">
                                <label for="postEvent" class="form-label">Economic Event</label>
                                <select class="form-select" id="postEvent" name="event_id">
                                    <option value="">Select Economic Event</option>
                                    <!-- Events will be loaded dynamically -->
                                </select>
                            </div>

                            <div class="mb-3" id="signalGroup">
                                <label for="postSignal" class="form-label">Trading Signal</label>
                                <select class="form-select" id="postSignal" name="signal_id">
                                    <option value="">Select Trading Signal</option>
                                    <!-- Signals will be loaded dynamically -->
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="postContent" class="form-label">Content</label>
                        <textarea class="form-control" id="postContent" name="content" rows="10" required></textarea>
                        <div class="form-text">Post content in HTML format</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="savePostBtn">Save Post</button>
            </div>
        </div>
    </div>
</div>

<!-- View Post Modal -->
<div class="modal fade" id="viewPostModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">View Post</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <div>
                            <span class="badge bg-primary me-2" id="viewPostType"></span>
                            <span class="badge bg-secondary me-2" id="viewPostChannel"></span>
                            <span class="badge" id="viewPostStatus"></span>
                        </div>
                        <div>
                            <small class="text-muted" id="viewPostTime"></small>
                        </div>
                    </div>
                </div>
                <div class="border rounded p-3 bg-light" id="viewPostContent" style="white-space: pre-wrap;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deletePostModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this post? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Send Now Confirmation Modal -->
<div class="modal fade" id="sendNowModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send Post Now</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to send post <strong>#<span id="sendNowPostId"></span></strong> to channel <strong><span id="sendNowChannelName"></span></strong> immediately?</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> This will send the post to Telegram right now and mark it as published.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmSendNowBtn">
                    <i class="fas fa-paper-plane"></i> Send Now
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    const pageSize = 10;
    let totalPosts = 0;
    let channels = [];

    $(document).ready(function() {
        console.log("Posts page loaded, initializing...");

        // Load channels
        loadChannels();

        // Add a debug button to check database state
        $('<button id="debugBtn" class="btn btn-sm btn-secondary ms-2">Debug</button>')
            .insertAfter('#refreshPostsBtn')
            .click(function() {
                console.log("Debug button clicked, checking posts in database...");
                $.ajax({
                    url: '/api/debug/posts',
                    type: 'GET',
                    success: function(data) {
                        console.log("Posts in database:", data);
                        alert(`Found ${data.total} posts in database. Check console for details.`);
                    },
                    error: function(xhr) {
                        console.error("Error fetching debug data:", xhr);
                        alert("Error fetching debug data. Check console for details.");
                    }
                });
            });

        // Load posts
        loadPosts();

        // Set up event handlers
        $('#refreshPostsBtn').click(function() {
            loadPosts();
        });

        $('#statusFilter, #typeFilter, #channelFilter').change(function() {
            currentPage = 1;
            loadPosts();
        });

        $('#createPostBtn').click(function() {
            resetPostForm();
            $('#postModalTitle').text('Create Post');
            $('#postModal').modal('show');
        });

        $('#savePostBtn').click(function() {
            savePost();
        });

        // Show/hide scheduled time based on status
        $('#postStatus').change(function() {
            if ($(this).val() === 'scheduled') {
                $('#scheduledTimeGroup').show();
            } else {
                $('#scheduledTimeGroup').hide();
            }
        });

        // Show/hide related fields based on post type
        $('#postType').change(function() {
            const type = $(this).val();

            $('#newsGroup, #eventGroup, #signalGroup').hide();

            if (type === 'news') {
                $('#newsGroup').show();
                loadNewsItems();
            } else if (type === 'event') {
                $('#eventGroup').show();
                loadEvents();
            } else if (type === 'signal' || type === 'performance') {
                $('#signalGroup').show();
                loadSignals();
            }
        });
    });

    function loadChannels() {
        $.ajax({
            url: '/api/channels',
            type: 'GET',
            success: function(data) {
                channels = data;

                // Populate channel filter
                let filterOptions = '<option value="">All Channels</option>';
                let formOptions = '<option value="">Select Channel</option>';

                data.forEach(function(channel) {
                    filterOptions += `<option value="${channel.id}">${channel.name}</option>`;
                    formOptions += `<option value="${channel.id}">${channel.name}</option>`;
                });

                $('#channelFilter').html(filterOptions);
                $('#postChannel').html(formOptions);
            }
        });
    }

    function loadNewsItems() {
        // This would be implemented to load news items for the dropdown
        // For now, we'll just add a placeholder
        $('#postNews').html('<option value="">Select News Item</option><option value="1">Sample News Item</option>');
    }

    function loadEvents() {
        // This would be implemented to load economic events for the dropdown
        // For now, we'll just add a placeholder
        $('#postEvent').html('<option value="">Select Economic Event</option><option value="1">Sample Economic Event</option>');
    }

    function loadSignals() {
        // This would be implemented to load trading signals for the dropdown
        // For now, we'll just add a placeholder
        $('#postSignal').html('<option value="">Select Trading Signal</option><option value="1">Sample Trading Signal</option>');
    }

    function loadPosts() {
        const statusFilter = $('#statusFilter').val();
        const typeFilter = $('#typeFilter').val();
        const channelFilter = $('#channelFilter').val();

        const offset = (currentPage - 1) * pageSize;

        // Show loading indicator
        $('#postsTable tbody').html(`
            <tr>
                <td colspan="7" class="text-center">
                    <div class="spinner-border text-primary" role="status"></div>
                    <p>Loading posts...</p>
                </td>
            </tr>
        `);

        // Build query parameters for server-side filtering
        let queryParams = `limit=${pageSize}&offset=${offset}`;

        // Add timestamp to prevent caching
        queryParams += `&_t=${new Date().getTime()}`;

        // Log for debugging
        console.log("Loading posts with filters:", { statusFilter, typeFilter, channelFilter });

        // First check if posts exist in the database
        $.ajax({
            url: '/api/debug/posts',
            type: 'GET',
            success: function(debugData) {
                console.log("Debug data:", debugData);
                console.log(`Database has ${debugData.total} total posts`);
                console.log("Status counts:", debugData.status_counts);
                console.log("Recent posts:", debugData.recent_posts);

                // Now load the posts for display
                $.ajax({
                    url: `/api/posts?${queryParams}`,
                    type: 'GET',
                    success: function(data) {
                        totalPosts = data.total;
                        console.log(`Received ${data.posts.length} posts from server, total: ${totalPosts}`);
                        console.log("Posts data:", data);

                        let tableHtml = '';
                        let filteredPosts = data.posts;

                        // Apply client-side filters if needed
                        if (statusFilter || typeFilter || channelFilter) {
                            filteredPosts = data.posts.filter(post => {
                                return (!statusFilter || post.status === statusFilter) &&
                                       (!typeFilter || post.type === typeFilter) &&
                                       (!channelFilter || post.channel_id === parseInt(channelFilter));
                            });
                            console.log(`After filtering: ${filteredPosts.length} posts match criteria`);
                        }

                        if (filteredPosts.length === 0) {
                            tableHtml = '<tr><td colspan="7" class="text-center">No posts found</td></tr>';
                        } else {
                            filteredPosts.forEach(function(post) {
                                // Find channel name
                                const channel = channels.find(c => c.id === post.channel_id);
                                const channelName = channel ? channel.name : 'Unknown';

                                // Safely encode post data for HTML attributes
                                const postDataAttr = encodeURIComponent(JSON.stringify(post));

                                tableHtml += `
                                    <tr>
                                        <td>${post.id}</td>
                                        <td>${channelName}</td>
                                        <td><span class="badge bg-primary">${post.type}</span></td>
                                        <td><span class="badge bg-${getStatusColor(post.status)}">${post.status}</span></td>
                                        <td>${post.scheduled_time ? formatDate(post.scheduled_time) : '-'}</td>
                                        <td>${post.published_time ? formatDate(post.published_time) : '-'}</td>
                                        <td>
                                            <button class="btn btn-sm btn-info view-post-btn" data-post-id="${post.id}" data-channel="${channelName}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary edit-post-btn" data-post-id="${post.id}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            ${post.status !== 'published' ? `
                                                <button class="btn btn-sm btn-success send-now-btn" data-post-id="${post.id}" data-channel="${channelName}">
                                                    <i class="fas fa-paper-plane"></i>
                                                </button>
                                            ` : ''}
                                            <button class="btn btn-sm btn-danger delete-post-btn" data-post-id="${post.id}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `;
                            });
                        }

                        $('#postsTable tbody').html(tableHtml);

                        // Update the posts count to show filtered count if filters are applied
                        if (statusFilter || typeFilter || channelFilter) {
                            $('#postsCount').text(filteredPosts.length);
                        } else {
                            $('#postsCount').text(totalPosts);
                        }

                        // Update pagination
                        updatePagination();

                        // Set up button handlers
                        $('.view-post-btn').click(function() {
                            const postId = $(this).data('post-id');
                            const channelName = $(this).data('channel');
                            fetchAndViewPost(postId, channelName);
                        });

                        $('.edit-post-btn').click(function() {
                            const postId = $(this).data('post-id');
                            fetchAndEditPost(postId);
                        });

                        $('.delete-post-btn').click(function() {
                            const postId = $(this).data('post-id');
                            showDeleteConfirmation(postId);
                        });

                        $('.send-now-btn').click(function() {
                            const postId = $(this).data('post-id');
                            const channelName = $(this).data('channel');
                            showSendNowConfirmation(postId, channelName);
                        });
                    },
                    error: function(xhr) {
                        console.error("Error loading posts:", xhr);
                        $('#postsTable tbody').html(`
                            <tr>
                                <td colspan="7" class="text-center text-danger">
                                    Error loading posts: ${xhr.responseJSON ? xhr.responseJSON.detail : xhr.statusText}
                                </td>
                            </tr>
                        `);
                    }
                });
            },
            error: function(xhr) {
                console.error("Error fetching debug data:", xhr);
                // Continue with normal post loading
                $.ajax({
                    url: `/api/posts?${queryParams}`,
                    type: 'GET',
                    success: function(data) {
                        totalPosts = data.total;
                        console.log(`Received ${data.posts.length} posts from server, total: ${totalPosts}`);

                        let tableHtml = '';
                        let filteredPosts = data.posts;

                        // Apply client-side filters if needed
                        if (statusFilter || typeFilter || channelFilter) {
                            filteredPosts = data.posts.filter(post => {
                                return (!statusFilter || post.status === statusFilter) &&
                                       (!typeFilter || post.type === typeFilter) &&
                                       (!channelFilter || post.channel_id === parseInt(channelFilter));
                            });
                            console.log(`After filtering: ${filteredPosts.length} posts match criteria`);
                        }
                        if (filteredPosts.length === 0) {
                            tableHtml = '<tr><td colspan="7" class="text-center">No posts found</td></tr>';
                        } else {
                            filteredPosts.forEach(function(post) {
                                // Find channel name
                                const channel = channels.find(c => c.id === post.channel_id);
                                const channelName = channel ? channel.name : 'Unknown';

                                // Safely encode post data for HTML attributes
                                const postDataAttr = encodeURIComponent(JSON.stringify(post));

                                tableHtml += `
                                    <tr>
                                        <td>${post.id}</td>
                                        <td>${channelName}</td>
                                        <td><span class="badge bg-primary">${post.type}</span></td>
                                        <td><span class="badge bg-${getStatusColor(post.status)}">${post.status}</span></td>
                                        <td>${post.scheduled_time ? formatDate(post.scheduled_time) : '-'}</td>
                                        <td>${post.published_time ? formatDate(post.published_time) : '-'}</td>
                                        <td>
                                            <button class="btn btn-sm btn-info view-post-btn" data-post-id="${post.id}" data-channel="${channelName}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary edit-post-btn" data-post-id="${post.id}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-post-btn" data-post-id="${post.id}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `;
                            });
                        }

                        $('#postsTable tbody').html(tableHtml);

                        // Update the posts count to show filtered count if filters are applied
                        if (statusFilter || typeFilter || channelFilter) {
                            $('#postsCount').text(filteredPosts.length);
                        } else {
                            $('#postsCount').text(totalPosts);
                        }

                        // Update pagination
                        updatePagination();

                        // Set up button handlers
                        $('.view-post-btn').click(function() {
                            const postId = $(this).data('post-id');
                            const channelName = $(this).data('channel');
                            fetchAndViewPost(postId, channelName);
                        });

                        $('.edit-post-btn').click(function() {
                            const postId = $(this).data('post-id');
                            fetchAndEditPost(postId);
                        });

                        $('.delete-post-btn').click(function() {
                            const postId = $(this).data('post-id');
                            showDeleteConfirmation(postId);
                        });

                        $('.send-now-btn').click(function() {
                            const postId = $(this).data('post-id');
                            const channelName = $(this).data('channel');
                            showSendNowConfirmation(postId, channelName);
                        });
                    },
                    error: function(xhr) {
                        console.error("Error loading posts:", xhr);
                        $('#postsTable tbody').html(`
                            <tr>
                                <td colspan="7" class="text-center text-danger">
                                    Error loading posts: ${xhr.responseJSON ? xhr.responseJSON.detail : xhr.statusText}
                                </td>
                            </tr>
                        `);
                    }
                });
            },
            error: function(xhr) {
                console.error("Error fetching debug data:", xhr);
                // Continue with normal post loading without debug data
                $.ajax({
                    url: `/api/posts?${queryParams}`,
                    type: 'GET',
                    success: function(data) {
                        totalPosts = data.total;
                        console.log(`Received ${data.posts.length} posts from server, total: ${totalPosts}`);

                        let tableHtml = '';
                        let filteredPosts = data.posts;

                        // Apply client-side filters if needed
                        if (statusFilter || typeFilter || channelFilter) {
                            filteredPosts = data.posts.filter(post => {
                                return (!statusFilter || post.status === statusFilter) &&
                                       (!typeFilter || post.type === typeFilter) &&
                                       (!channelFilter || post.channel_id === parseInt(channelFilter));
                            });
                            console.log(`After filtering: ${filteredPosts.length} posts match criteria`);
                        }

                        if (filteredPosts.length === 0) {
                            tableHtml = '<tr><td colspan="7" class="text-center">No posts found</td></tr>';
                        } else {
                            filteredPosts.forEach(function(post) {
                                // Find channel name
                                const channel = channels.find(c => c.id === post.channel_id);
                                const channelName = channel ? channel.name : 'Unknown';

                                // Safely encode post data for HTML attributes
                                const postDataAttr = encodeURIComponent(JSON.stringify(post));

                                tableHtml += `
                                    <tr>
                                        <td>${post.id}</td>
                                        <td>${channelName}</td>
                                        <td><span class="badge bg-primary">${post.type}</span></td>
                                        <td><span class="badge bg-${getStatusColor(post.status)}">${post.status}</span></td>
                                        <td>${post.scheduled_time ? formatDate(post.scheduled_time) : '-'}</td>
                                        <td>${post.published_time ? formatDate(post.published_time) : '-'}</td>
                                        <td>
                                            <button class="btn btn-sm btn-info view-post-btn" data-post-id="${post.id}" data-channel="${channelName}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary edit-post-btn" data-post-id="${post.id}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-post-btn" data-post-id="${post.id}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `;
                            });
                        }

                        $('#postsTable tbody').html(tableHtml);

                        // Update the posts count to show filtered count if filters are applied
                        if (statusFilter || typeFilter || channelFilter) {
                            $('#postsCount').text(filteredPosts.length);
                        } else {
                            $('#postsCount').text(totalPosts);
                        }

                        // Update pagination
                        updatePagination();

                        // Set up button handlers
                        $('.view-post-btn').click(function() {
                            const postId = $(this).data('post-id');
                            const channelName = $(this).data('channel');
                            fetchAndViewPost(postId, channelName);
                        });

                        $('.edit-post-btn').click(function() {
                            const postId = $(this).data('post-id');
                            fetchAndEditPost(postId);
                        });

                        $('.delete-post-btn').click(function() {
                            const postId = $(this).data('post-id');
                            showDeleteConfirmation(postId);
                        });

                        $('.send-now-btn').click(function() {
                            const postId = $(this).data('post-id');
                            const channelName = $(this).data('channel');
                            showSendNowConfirmation(postId, channelName);
                        });
                    },
                    error: function(xhr) {
                        $('#postsTable tbody').html(`
                            <tr>
                                <td colspan="7" class="text-center text-danger">
                                    Error loading posts: ${xhr.responseJSON ? xhr.responseJSON.detail : xhr.statusText}
                                </td>
                            </tr>
                        `);
                    }
                });
            }
        });
    }

    function updatePagination() {
        // If we have filters applied, we need to adjust the pagination
        const statusFilter = $('#statusFilter').val();
        const typeFilter = $('#typeFilter').val();
        const channelFilter = $('#channelFilter').val();

        // Use total posts count for pagination
        const totalPages = Math.ceil(totalPosts / pageSize);

        console.log(`Pagination: ${totalPosts} total posts, ${totalPages} pages, current page: ${currentPage}`);

        let paginationHtml = '';

        // Previous button
        paginationHtml += `
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>
            </li>
        `;

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                paginationHtml += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `;
            } else if (i === currentPage - 3 || i === currentPage + 3) {
                paginationHtml += `
                    <li class="page-item disabled">
                        <a class="page-link" href="#">...</a>
                    </li>
                `;
            }
        }

        // Next button
        paginationHtml += `
            <li class="page-item ${currentPage === totalPages || totalPages === 0 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>
            </li>
        `;

        $('#postsPagination').html(paginationHtml);

        // Set up pagination click handlers
        $('.page-link').click(function(e) {
            e.preventDefault();

            if ($(this).parent().hasClass('disabled')) {
                return;
            }

            currentPage = parseInt($(this).data('page'));
            loadPosts();
        });
    }

    function resetPostForm() {
        $('#postId').val('');
        $('#postForm')[0].reset();

        // Set default values using server timezone
        const now = new Date();
        const serverTime = new Date(now.getTime() + serverTimeOffset);
        serverTime.setMinutes(serverTime.getMinutes() + 5);

        // Format for datetime-local input
        const formattedTime = serverTime.getFullYear() + '-' +
            String(serverTime.getMonth() + 1).padStart(2, '0') + '-' +
            String(serverTime.getDate()).padStart(2, '0') + 'T' +
            String(serverTime.getHours()).padStart(2, '0') + ':' +
            String(serverTime.getMinutes()).padStart(2, '0');

        $('#postScheduledTime').val(formattedTime);

        // Show/hide fields based on initial values
        if ($('#postStatus').val() === 'scheduled') {
            $('#scheduledTimeGroup').show();
        } else {
            $('#scheduledTimeGroup').hide();
        }

        const type = $('#postType').val();
        $('#newsGroup, #eventGroup, #signalGroup').hide();

        if (type === 'news') {
            $('#newsGroup').show();
            loadNewsItems();
        } else if (type === 'event') {
            $('#eventGroup').show();
            loadEvents();
        } else if (type === 'signal' || type === 'performance') {
            $('#signalGroup').show();
            loadSignals();
        }
    }

    function editPost(post) {
        resetPostForm();

        $('#postId').val(post.id);
        $('#postChannel').val(post.channel_id);
        $('#postType').val(post.type);
        $('#postStatus').val(post.status);
        $('#postContent').val(post.content);

        if (post.scheduled_time) {
            try {
                // Parse the scheduled time (stored in UTC)
                const scheduledDate = new Date(post.scheduled_time);

                // Convert to server timezone for display
                const serverTime = new Date(scheduledDate.getTime() + serverTimeOffset);

                console.log('Post scheduled time (UTC):', post.scheduled_time);
                console.log('Post scheduled time (server timezone):', serverTime.toISOString());

                // Format for datetime-local input
                const formattedTime = serverTime.getFullYear() + '-' +
                    String(serverTime.getMonth() + 1).padStart(2, '0') + '-' +
                    String(serverTime.getDate()).padStart(2, '0') + 'T' +
                    String(serverTime.getHours()).padStart(2, '0') + ':' +
                    String(serverTime.getMinutes()).padStart(2, '0');

                $('#postScheduledTime').val(formattedTime);
            } catch (e) {
                console.error('Error parsing scheduled time:', e);
                // Fallback to original value if parsing fails
                $('#postScheduledTime').val(post.scheduled_time.slice(0, 16));
            }

            $('#scheduledTimeGroup').show();
        } else {
            $('#scheduledTimeGroup').hide();
        }

        // Show/hide related fields based on post type
        $('#newsGroup, #eventGroup, #signalGroup').hide();

        if (post.type === 'news' && post.news_id) {
            $('#newsGroup').show();
            loadNewsItems();
            $('#postNews').val(post.news_id);
        } else if (post.type === 'event' && post.event_id) {
            $('#eventGroup').show();
            loadEvents();
            $('#postEvent').val(post.event_id);
        } else if ((post.type === 'signal' || post.type === 'performance') && post.signal_id) {
            $('#signalGroup').show();
            loadSignals();
            $('#postSignal').val(post.signal_id);
        }

        $('#postModalTitle').text('Edit Post');
        $('#postModal').modal('show');
    }

    function viewPost(post, channelName) {
        $('#viewPostType').text(post.type).removeClass().addClass(`badge bg-primary`);
        $('#viewPostChannel').text(channelName);
        $('#viewPostStatus').text(post.status).removeClass().addClass(`badge bg-${getStatusColor(post.status)}`);

        let timeText = '';
        if (post.published_time) {
            timeText = `Published: ${formatDate(post.published_time)}`;
        } else if (post.scheduled_time) {
            timeText = `Scheduled: ${formatDate(post.scheduled_time)}`;
        } else {
            timeText = `Created: ${formatDate(post.created_at)}`;
        }

        $('#viewPostTime').text(timeText);
        $('#viewPostContent').html(post.content);

        $('#viewPostModal').modal('show');
    }

    function savePost() {
        const postId = $('#postId').val();

        const formData = {
            channel_id: parseInt($('#postChannel').val()),
            type: $('#postType').val(),
            status: $('#postStatus').val(),
            content: $('#postContent').val()
        };

        // Add scheduled time if status is scheduled
        if (formData.status === 'scheduled') {
            try {
                // Get the scheduled time from the form
                const scheduledTimeStr = $('#postScheduledTime').val();

                // Parse the local datetime-local value
                const localDate = new Date(scheduledTimeStr);

                // Convert to ISO string for the server
                // We'll preserve the time exactly as entered, without applying browser's timezone offset
                // This ensures the time is interpreted as being in the server's timezone
                formData.scheduled_time = localDate.toISOString();

                console.log('Scheduled time (form):', scheduledTimeStr);
                console.log('Scheduled time (ISO):', formData.scheduled_time);
            } catch (e) {
                console.error('Error converting scheduled time:', e);
                // Fallback to original value if conversion fails
                formData.scheduled_time = $('#postScheduledTime').val();
            }
        }

        // Add related item ID based on post type
        if (formData.type === 'news') {
            const newsId = $('#postNews').val();
            if (newsId) {
                formData.news_id = parseInt(newsId);
            }
        } else if (formData.type === 'event') {
            const eventId = $('#postEvent').val();
            if (eventId) {
                formData.event_id = parseInt(eventId);
            }
        } else if (formData.type === 'signal' || formData.type === 'performance') {
            const signalId = $('#postSignal').val();
            if (signalId) {
                formData.signal_id = parseInt(signalId);
            }
        }

        $.ajax({
            url: postId ? `/api/posts/${postId}` : '/api/posts',
            type: postId ? 'PUT' : 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                $('#postModal').modal('hide');
                loadPosts();
                alert(`Post ${postId ? 'updated' : 'created'} successfully!`);
            },
            error: function(xhr) {
                alert(`Error ${postId ? 'updating' : 'creating'} post: ` + xhr.responseJSON.detail);
            }
        });
    }

    function showDeleteConfirmation(postId) {
        $('#confirmDeleteBtn').data('post-id', postId);
        $('#deletePostModal').modal('show');

        // Set up delete confirmation button
        $('#confirmDeleteBtn').off('click').click(function() {
            const id = $(this).data('post-id');
            deletePost(id);
        });
    }

    function deletePost(postId) {
        $.ajax({
            url: `/api/posts/${postId}`,
            type: 'DELETE',
            success: function(response) {
                $('#deletePostModal').modal('hide');
                loadPosts();
                alert('Post deleted successfully!');
            },
            error: function(xhr) {
                alert('Error deleting post: ' + xhr.responseJSON.detail);
            }
        });
    }

    function showSendNowConfirmation(postId, channelName) {
        $('#sendNowPostId').text(postId);
        $('#sendNowChannelName').text(channelName);
        $('#confirmSendNowBtn').data('post-id', postId);
        $('#sendNowModal').modal('show');

        // Set up send confirmation button
        $('#confirmSendNowBtn').off('click').click(function() {
            const id = $(this).data('post-id');
            sendPostNow(id);
        });
    }

    function sendPostNow(postId) {
        // Show loading state
        $('#confirmSendNowBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Sending...');

        $.ajax({
            url: `/api/posts/${postId}/send`,
            type: 'POST',
            success: function(response) {
                $('#sendNowModal').modal('hide');
                loadPosts();
                alert('Post sent successfully!');
            },
            error: function(xhr) {
                alert('Error sending post: ' + (xhr.responseJSON?.detail || 'Unknown error'));
            },
            complete: function() {
                // Reset button state
                $('#confirmSendNowBtn').prop('disabled', false).html('<i class="fas fa-paper-plane"></i> Send Now');
            }
        });
    }

    function getStatusColor(status) {
        switch (status) {
            case 'draft': return 'primary';
            case 'scheduled': return 'warning';
            case 'published': return 'success';
            case 'failed': return 'danger';
            default: return 'secondary';
        }
    }

    function formatDate(dateString) {
        if (!dateString) return '-';

        try {
            // Parse the date string (stored in UTC)
            const date = new Date(dateString);

            // Check if the date is valid
            if (isNaN(date.getTime())) {
                console.error('Invalid date:', dateString);
                return dateString;
            }

            // Convert UTC to server timezone for display
            const serverTime = new Date(date.getTime() + serverTimeOffset);

            // Format the date with timezone info
            const formattedDate = serverTime.toLocaleString('en-GB', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });

            return formattedDate + ' (Asia/Tehran)';
        } catch (e) {
            console.error('Error formatting date:', e);
            return dateString;
        }
    }

    // Debug function to show timezone information
    function logTimezoneInfo() {
        console.log('Browser timezone:', Intl.DateTimeFormat().resolvedOptions().timeZone);
        console.log('Browser offset (minutes):', new Date().getTimezoneOffset() * -1);
        console.log('Server timezone:', serverTimezone);
        console.log('Server time offset (ms):', serverTimeOffset);

        // Current time in different formats
        const now = new Date();
        console.log('Current time (local):', now.toString());
        console.log('Current time (UTC):', now.toUTCString());
        console.log('Current time (ISO):', now.toISOString());

        // Server time
        const serverTime = new Date(now.getTime() + serverTimeOffset);
        console.log('Current server time:', serverTime.toString());
    }

    // Log timezone info on page load
    $(document).ready(function() {
        logTimezoneInfo();
    });

    function fetchAndViewPost(postId, channelName) {
        // Show loading indicator
        $('#viewPostContent').html('<div class="text-center"><div class="spinner-border text-primary" role="status"></div><p>Loading post content...</p></div>');
        $('#viewPostModal').modal('show');

        // Fetch post data
        $.ajax({
            url: `/api/posts/${postId}`,
            type: 'GET',
            success: function(post) {
                viewPost(post, channelName);
            },
            error: function(xhr) {
                $('#viewPostContent').html(`<div class="text-danger">Error loading post: ${xhr.responseJSON.detail}</div>`);
            }
        });
    }

    function fetchAndEditPost(postId) {
        // Show loading indicator in the form
        $('#postContent').val('Loading post content...');
        $('#postModal').modal('show');

        // Fetch post data
        $.ajax({
            url: `/api/posts/${postId}`,
            type: 'GET',
            success: function(post) {
                editPost(post);
            },
            error: function(xhr) {
                alert(`Error loading post: ${xhr.responseJSON.detail}`);
                $('#postModal').modal('hide');
            }
        });
    }
</script>
{% endblock %}
