# 🚀 MignalyBot Deployment Guide

This guide provides comprehensive instructions for deploying MignalyBot on a server using Docker.

## 📋 Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+ recommended)
- **RAM**: Minimum 2GB, Recommended 4GB+
- **Storage**: Minimum 10GB free space
- **Network**: Internet connection for API access

### Required Software
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+ (optional)
- **Git**: For cloning the repository

## 🔧 Pre-Deployment Setup

### 1. Install Docker

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add user to docker group
sudo usermod -aG docker $USER

# Start and enable Docker
sudo systemctl start docker
sudo systemctl enable docker

# Verify installation
docker --version
```

### 2. Clone Repository

```bash
# Clone the repository
git clone https://github.com/daadbina/mignalybot.git
cd mignalybot
```

### 3. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

**Required Environment Variables:**

```env
# Database Configuration
DATABASE_URL=sqlite:///mignalybot.db

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# AI Configuration (Qwen API)
QWEN_API_KEY=your_qwen_api_key_here
QWEN_API_URL=https://dashscope-intl.aliyuncs.com/compatible-mode/v1

# Application Configuration
TIMEZONE=Asia/Tehran
DEBUG=False
LOG_LEVEL=INFO

# Admin Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# Server Configuration
HOST=0.0.0.0
PORT=8000
```

## 🚀 Deployment Methods

### Method 1: Automated Docker Deployment (Recommended)

```bash
# Make deployment script executable
chmod +x scripts/docker_deploy.sh

# Run automated deployment
./scripts/docker_deploy.sh deploy
```

This script will:
1. ✅ Check Docker installation
2. ✅ Validate environment configuration
3. ✅ Build Docker image
4. ✅ Stop existing containers
5. ✅ Run database migrations
6. ✅ Setup prompt templates
7. ✅ Create default admin user
8. ✅ Start application container
9. ✅ Verify deployment

### Method 2: Manual Docker Deployment

```bash
# 1. Build Docker image
docker build -t mignalybot:latest .

# 2. Run database setup
docker run --rm \
  --env-file .env \
  -v "$(pwd)/logs:/app/logs" \
  mignalybot:latest \
  python scripts/deploy_mignalybot.py

# 3. Start application container
docker run -d \
  --name mignalybot \
  --restart unless-stopped \
  -p 9000:8000 \
  --env-file .env \
  -v "$(pwd)/logs:/app/logs" \
  -v "$(pwd)/data:/app/data" \
  mignalybot:latest
```

### Method 3: Python Virtual Environment

```bash
# 1. Create virtual environment
python3 -m venv venv
source venv/bin/activate

# 2. Install dependencies
pip install -r requirements.txt

# 3. Run deployment script
python scripts/deploy_mignalybot.py

# 4. Start application
python main.py
```

## 🔍 Post-Deployment Verification

### 1. Check Container Status

```bash
# View running containers
docker ps

# Check application logs
docker logs mignalybot

# Follow logs in real-time
docker logs -f mignalybot
```

### 2. Access Admin Interface

1. **Open browser**: `http://your-server-ip:9000`
2. **Login**: Username: `admin`, Password: `admin123`
3. **Verify features**:
   - ✅ Dashboard loads correctly
   - ✅ Prompt Templates page shows 24 templates
   - ✅ Configuration settings accessible
   - ✅ Channel management available

### 3. Test API Endpoints

```bash
# Health check
curl http://localhost:9000/health

# Prompt templates API
curl -u admin:admin123 http://localhost:9000/api/prompt-templates

# Configuration API
curl -u admin:admin123 http://localhost:9000/api/config
```

## 🛠️ Management Commands

### Container Management

```bash
# Stop MignalyBot
./scripts/docker_deploy.sh stop

# Restart MignalyBot
./scripts/docker_deploy.sh restart

# View logs
./scripts/docker_deploy.sh logs

# Check status
./scripts/docker_deploy.sh status

# Rebuild and redeploy
./scripts/docker_deploy.sh rebuild
```

### Database Operations

```bash
# Run migrations manually
docker exec mignalybot python scripts/deploy_mignalybot.py

# Setup prompt templates
docker exec mignalybot python scripts/setup_prompt_system.py

# Access database shell (SQLite)
docker exec -it mignalybot sqlite3 mignalybot.db
```

### Backup and Restore

```bash
# Backup database
docker exec mignalybot cp mignalybot.db /app/data/backup_$(date +%Y%m%d_%H%M%S).db

# Backup logs
docker cp mignalybot:/app/logs ./backup_logs_$(date +%Y%m%d_%H%M%S)

# Restore database
docker cp backup_database.db mignalybot:/app/mignalybot.db
docker restart mignalybot
```

## 🔧 Configuration Management

### Update Environment Variables

```bash
# 1. Stop container
docker stop mignalybot

# 2. Edit .env file
nano .env

# 3. Restart with new configuration
./scripts/docker_deploy.sh restart
```

### Update Prompt Templates

1. **Access admin interface**: `http://your-server-ip:9000/prompts`
2. **Edit templates**: Click on any template card
3. **Test changes**: Use the "Test" button
4. **Save**: Changes are applied immediately

### Add New Languages

1. **Access prompt templates**: Admin → Prompt Templates
2. **Create new template**: Click "Add Template"
3. **Select language**: Choose target language
4. **Copy base template**: Use Farsi template as starting point
5. **Translate and adapt**: Modify for target language
6. **Test and save**: Verify with AI generation

## 🚨 Troubleshooting

### Common Issues

**1. Container won't start**
```bash
# Check logs
docker logs mignalybot

# Verify environment variables
docker exec mignalybot env | grep -E "(TELEGRAM|QWEN|DATABASE)"

# Check port conflicts
sudo netstat -tulpn | grep :9000
```

**2. Database connection errors**
```bash
# Check database file permissions
docker exec mignalybot ls -la mignalybot.db

# Recreate database
docker exec mignalybot python scripts/deploy_mignalybot.py
```

**3. API connection failures**
```bash
# Test external APIs
docker exec mignalybot curl -s http://*************:8000/api/market/job-status
docker exec mignalybot curl -s https://nfs.faireconomy.media/ff_calendar_thisweek.json

# Verify Qwen API key
docker exec mignalybot python -c "import os; print('QWEN_API_KEY:', bool(os.getenv('QWEN_API_KEY')))"
```

**4. Prompt templates not loading**
```bash
# Check prompt templates in database
docker exec mignalybot python -c "
import asyncio
from scripts.setup_prompt_system import verify_system
asyncio.run(verify_system())
"

# Repopulate prompt templates
docker exec mignalybot python scripts/setup_prompt_system.py
```

### Performance Optimization

**1. Resource Limits**
```bash
# Run with memory limits
docker run -d \
  --name mignalybot \
  --memory=2g \
  --cpus=1.0 \
  --restart unless-stopped \
  -p 9000:8000 \
  --env-file .env \
  mignalybot:latest
```

**2. Log Rotation**
```bash
# Configure log rotation
docker run -d \
  --name mignalybot \
  --log-driver json-file \
  --log-opt max-size=10m \
  --log-opt max-file=3 \
  --restart unless-stopped \
  -p 9000:8000 \
  --env-file .env \
  mignalybot:latest
```

## 🔒 Security Considerations

### 1. Change Default Credentials
```bash
# Update admin password in admin interface
# Or set custom credentials in .env:
ADMIN_USERNAME=your_admin_username
ADMIN_PASSWORD=your_secure_password
```

### 2. Firewall Configuration
```bash
# Allow only necessary ports
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 9000/tcp  # MignalyBot
sudo ufw enable
```

### 3. SSL/TLS Setup (Optional)
```bash
# Use reverse proxy (nginx) with SSL
# Configure Let's Encrypt certificates
# Update port mapping to use HTTPS
```

## 📊 Monitoring and Maintenance

### Health Monitoring
```bash
# Create health check script
cat > health_check.sh << 'EOF'
#!/bin/bash
if curl -f http://localhost:9000/health > /dev/null 2>&1; then
    echo "$(date): MignalyBot is healthy"
else
    echo "$(date): MignalyBot health check failed"
    # Optional: restart container
    # docker restart mignalybot
fi
EOF

# Add to crontab for regular checks
chmod +x health_check.sh
(crontab -l 2>/dev/null; echo "*/5 * * * * /path/to/health_check.sh >> /var/log/mignalybot_health.log") | crontab -
```

### Log Management
```bash
# View recent logs
docker logs --tail 100 mignalybot

# Search logs for errors
docker logs mignalybot 2>&1 | grep -i error

# Export logs for analysis
docker logs mignalybot > mignalybot_logs_$(date +%Y%m%d).log
```

## 🎯 Next Steps

After successful deployment:

1. **Configure Telegram Channels**
   - Add your channels in the admin interface
   - Set up channel-specific settings
   - Configure posting schedules

2. **Customize Prompt Templates**
   - Edit prompts for your specific needs
   - Add support for additional languages
   - Test content generation

3. **Monitor Performance**
   - Check logs regularly
   - Monitor resource usage
   - Set up alerts for failures

4. **Regular Maintenance**
   - Update environment variables as needed
   - Backup database regularly
   - Keep Docker images updated

## 📞 Support

For issues and support:
- Check logs: `docker logs mignalybot`
- Review this guide for troubleshooting steps
- Verify all environment variables are correctly set
- Ensure external APIs are accessible

---

**🎉 Congratulations! MignalyBot is now deployed and ready to generate trading content for your Telegram channels.**
