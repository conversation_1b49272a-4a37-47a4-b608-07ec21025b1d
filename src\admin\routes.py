"""
API routes for MignalyBot admin dashboard
"""

import logging
import os
from datetime import datetime, timezone, timedelta
from typing import List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from sqlalchemy import select, func, delete
from pydantic import BaseModel

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import (
    Config, Channel, Strategy, NewsItem, EconomicEvent,
    TradingSignal as Signal, Post, PostType, PostStatus, SignalStatus, PromptTemplate
)
from src.ai_integration.qwen_client import QwenClient
from src.ai_integration.content_generator import generate_content, calculate_pips, get_performance_caption, is_forex_pair
from src.data_collection.scheduler import data_collection_task
from src.data_collection.news import collect_news as collect_news_task
from src.data_collection.economic_calendar import collect_economic_events as collect_events_task
from src.strategies.processor import process_strategies as process_strategies_task
from src.utils.duplicate_cleaner import delete_duplicate_events, get_duplicate_events_count, cleanup_old_events
from src.utils.helpers import get_timezone

logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic models for API requests and responses
class ConfigModel(BaseModel):
    qwen_api_key: str
    default_language: str
    post_frequency: int
    symbols: str
    timeframes: str
    enable_news: bool
    enable_signals: bool
    enable_calendar: bool
    max_tokens_per_request: int

    class Config:
        arbitrary_types_allowed = True

class ChannelModel(BaseModel):
    id: Optional[int] = None
    chat_id: str
    name: str
    description: Optional[str] = None
    language: str
    active: bool
    brand_name: Optional[str] = None
    brand_description: Optional[str] = None
    enable_advertisement: bool = False
    advertisement_text: str = "This message generated by Mignaly"
    advertisement_url: str = "https://mignaly.com"
    post_types: str
    enable_date_stickers: bool = True
    date_sticker_style: str = "modern"

    class Config:
        arbitrary_types_allowed = True

class StrategyModel(BaseModel):
    id: Optional[int] = None
    name: str
    description: Optional[str] = None
    symbols: str
    timeframes: str
    parameters: dict
    strategy_prompt: str
    strategy_type: Optional[str] = "general"
    code: Optional[str] = ""  # Legacy field, optional
    active: bool

    class Config:
        arbitrary_types_allowed = True

class PostModel(BaseModel):
    channel_id: int
    type: str
    content: str
    status: str
    scheduled_time: Optional[str] = None
    news_id: Optional[int] = None
    event_id: Optional[int] = None
    signal_id: Optional[int] = None

    class Config:
        arbitrary_types_allowed = True

class PromptTemplateModel(BaseModel):
    id: Optional[int] = None
    post_type: str
    language: str
    template_content: str
    description: Optional[str] = None
    active: bool = True

    class Config:
        arbitrary_types_allowed = True

# System info routes
@router.get("/system/timezone")
async def get_timezone_info():
    """Get the server's timezone information"""
    from src.utils.helpers import get_timezone, get_current_time, format_datetime_with_timezone

    # Get the timezone from the environment
    tz = get_timezone()

    # Get current time in this timezone
    now = get_current_time()

    return {
        "timezone": str(tz),
        "current_time": now.strftime("%Y-%m-%d %H:%M:%S"),
        "utc_offset": now.strftime("%z"),
        "timezone_name": tz.zone,
        "timezone_display": f"{tz.zone} ({now.strftime('%z')})"
    }

# Debug routes
@router.get("/debug/posts")
async def debug_posts():
    """Debug endpoint to get information about posts in the database"""
    async for db in get_async_db():
        # Get total count
        if is_sqlite_db():
            count_result = db.execute(select(func.count(Post.id)))
            total_count = count_result.scalar()

            # Get recent posts
            recent_posts_result = db.execute(
                select(Post)
                .order_by(Post.id.desc())
                .limit(10)
            )
        else:
            count_result = await db.execute(select(func.count(Post.id)))
            total_count = count_result.scalar()

            # Get recent posts
            recent_posts_result = await db.execute(
                select(Post)
                .order_by(Post.id.desc())
                .limit(10)
            )

        recent_posts = recent_posts_result.scalars().all()

        # Get counts by status
        status_counts = {}
        for status in PostStatus:
            if is_sqlite_db():
                status_count_result = db.execute(
                    select(func.count(Post.id))
                    .where(Post.status == status)
                )
                status_counts[status.value] = status_count_result.scalar()
            else:
                status_count_result = await db.execute(
                    select(func.count(Post.id))
                    .where(Post.status == status)
                )
                status_counts[status.value] = status_count_result.scalar()

        return {
            "total": total_count,
            "status_counts": status_counts,
            "recent_posts": [
                {
                    "id": post.id,
                    "channel_id": post.channel_id,
                    "type": post.type.value,
                    "status": post.status.value,
                    "scheduled_time": post.scheduled_time,
                    "created_at": post.created_at
                }
                for post in recent_posts
            ]
        }

# Config routes
@router.get("/config", response_model=ConfigModel)
async def get_config():
    """Get global configuration"""
    async for db in get_async_db():
        if is_sqlite_db():
            result = db.execute(select(Config))
        else:
            result = await db.execute(select(Config))

        config = result.scalars().first()

        if not config:
            raise HTTPException(status_code=404, detail="Configuration not found")

        return ConfigModel(
            qwen_api_key=config.qwen_api_key,
            default_language=config.default_language,
            post_frequency=config.post_frequency,
            symbols=config.symbols,
            timeframes=config.timeframes,
            enable_news=config.enable_news,
            enable_signals=config.enable_signals,
            enable_calendar=config.enable_calendar,
            max_tokens_per_request=config.max_tokens_per_request
        )

@router.post("/config", response_model=ConfigModel)
async def update_config(config_data: ConfigModel):
    """Update global configuration"""
    async for db in get_async_db():
        # Handle SQLite differently than other databases
        if is_sqlite_db():
            result = db.execute(select(Config))
        else:
            result = await db.execute(select(Config))

        config = result.scalars().first()

        if not config:
            # Create new config if it doesn't exist
            config = Config(
                qwen_api_key=config_data.qwen_api_key,
                default_language=config_data.default_language,
                post_frequency=config_data.post_frequency,
                symbols=config_data.symbols,
                timeframes=config_data.timeframes,
                enable_news=config_data.enable_news,
                enable_signals=config_data.enable_signals,
                enable_calendar=config_data.enable_calendar,
                max_tokens_per_request=config_data.max_tokens_per_request
            )
            db.add(config)
        else:
            # Update existing config
            config.qwen_api_key = config_data.qwen_api_key
            config.default_language = config_data.default_language
            config.post_frequency = config_data.post_frequency
            config.symbols = config_data.symbols
            config.timeframes = config_data.timeframes
            config.enable_news = config_data.enable_news
            config.enable_signals = config_data.enable_signals
            config.enable_calendar = config_data.enable_calendar
            config.max_tokens_per_request = config_data.max_tokens_per_request
            config.updated_at = datetime.now(timezone.utc)

        # Handle commit based on database type
        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return config_data

# Channel routes
@router.get("/channels", response_model=List[ChannelModel])
async def get_channels():
    """Get all channels"""
    async for db in get_async_db():
        # Handle SQLite differently than other databases
        if is_sqlite_db():
            result = db.execute(select(Channel))
        else:
            result = await db.execute(select(Channel))
        channels = result.scalars().all()

        return [
            ChannelModel(
                id=channel.id,
                chat_id=channel.chat_id,
                name=channel.name,
                description=channel.description,
                language=channel.language,
                active=channel.active,
                brand_name=channel.brand_name,
                brand_description=channel.brand_description,
                enable_advertisement=getattr(channel, 'enable_advertisement', False),
                advertisement_text=getattr(channel, 'advertisement_text', "This message generated by Mignaly"),
                advertisement_url=getattr(channel, 'advertisement_url', "https://mignaly.com"),
                post_types=channel.post_types,
                enable_date_stickers=getattr(channel, 'enable_date_stickers', True),
                date_sticker_style=getattr(channel, 'date_sticker_style', 'modern')
            )
            for channel in channels
        ]

@router.post("/channels", response_model=ChannelModel)
async def create_channel(channel_data: ChannelModel):
    """Create a new channel"""
    logger.info(f"📢 Creating new channel: {channel_data.name}")
    logger.info(f"🔗 Chat ID: {channel_data.chat_id}")
    logger.info(f"📢 Advertisement enabled: {channel_data.enable_advertisement}")
    logger.info(f"📝 Advertisement text: '{channel_data.advertisement_text}'")
    logger.info(f"🔗 Advertisement URL: '{channel_data.advertisement_url}'")

    async for db in get_async_db():
        # Check if channel with this chat_id already exists
        if is_sqlite_db():
            result = db.execute(
                select(Channel).where(Channel.chat_id == channel_data.chat_id)
            )
        else:
            result = await db.execute(
                select(Channel).where(Channel.chat_id == channel_data.chat_id)
            )
        existing_channel = result.scalars().first()

        if existing_channel:
            raise HTTPException(status_code=400, detail="Channel with this chat_id already exists")

        # Create new channel
        channel = Channel(
            chat_id=channel_data.chat_id,
            name=channel_data.name,
            description=channel_data.description,
            language=channel_data.language,
            active=channel_data.active,
            brand_name=channel_data.brand_name,
            brand_description=channel_data.brand_description,
            enable_advertisement=channel_data.enable_advertisement,
            advertisement_text=channel_data.advertisement_text,
            advertisement_url=channel_data.advertisement_url,
            post_types=channel_data.post_types,
            enable_date_stickers=channel_data.enable_date_stickers,
            date_sticker_style=channel_data.date_sticker_style
        )

        logger.info(f"💾 Saving channel to database with advertisement settings:")
        logger.info(f"   📢 enable_advertisement: {channel.enable_advertisement}")
        logger.info(f"   📝 advertisement_text: '{channel.advertisement_text}'")
        logger.info(f"   🔗 advertisement_url: '{channel.advertisement_url}'")

        db.add(channel)
        # Handle commit based on database type
        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        logger.info(f"✅ Channel {channel_data.name} created successfully with ID: {channel.id}")
        return channel_data

@router.put("/channels/{channel_id}", response_model=ChannelModel)
async def update_channel(channel_id: int, channel_data: ChannelModel):
    """Update a channel"""
    logger.info(f"📝 Updating channel {channel_id}: {channel_data.name}")
    logger.info(f"📢 Advertisement enabled: {channel_data.enable_advertisement}")
    logger.info(f"📝 Advertisement text: '{channel_data.advertisement_text}'")
    logger.info(f"🔗 Advertisement URL: '{channel_data.advertisement_url}'")

    async for db in get_async_db():
        # Handle SQLite differently than other databases
        if is_sqlite_db():
            result = db.execute(
                select(Channel).where(Channel.id == channel_id)
            )
        else:
            result = await db.execute(
                select(Channel).where(Channel.id == channel_id)
            )
        channel = result.scalars().first()

        if not channel:
            raise HTTPException(status_code=404, detail="Channel not found")

        logger.info(f"📋 Current channel advertisement settings:")
        logger.info(f"   📢 enable_advertisement: {channel.enable_advertisement}")
        logger.info(f"   📝 advertisement_text: '{channel.advertisement_text}'")
        logger.info(f"   🔗 advertisement_url: '{channel.advertisement_url}'")

        # Update channel
        channel.chat_id = channel_data.chat_id
        channel.name = channel_data.name
        channel.description = channel_data.description
        channel.language = channel_data.language
        channel.active = channel_data.active
        channel.brand_name = channel_data.brand_name
        channel.brand_description = channel_data.brand_description
        channel.enable_advertisement = channel_data.enable_advertisement
        channel.advertisement_text = channel_data.advertisement_text
        channel.advertisement_url = channel_data.advertisement_url
        channel.post_types = channel_data.post_types
        channel.enable_date_stickers = channel_data.enable_date_stickers
        channel.date_sticker_style = channel_data.date_sticker_style
        channel.updated_at = datetime.now(timezone.utc)

        logger.info(f"💾 Updated channel advertisement settings:")
        logger.info(f"   📢 enable_advertisement: {channel.enable_advertisement}")
        logger.info(f"   📝 advertisement_text: '{channel.advertisement_text}'")
        logger.info(f"   🔗 advertisement_url: '{channel.advertisement_url}'")

        # Handle commit based on database type
        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        logger.info(f"✅ Channel {channel_data.name} updated successfully")
        return channel_data

@router.delete("/channels/{channel_id}")
async def delete_channel(channel_id: int):
    """Delete a channel"""
    async for db in get_async_db():
        # Handle SQLite differently than other databases
        if is_sqlite_db():
            result = db.execute(
                select(Channel).where(Channel.id == channel_id)
            )
        else:
            result = await db.execute(
                select(Channel).where(Channel.id == channel_id)
            )
        channel = result.scalars().first()

        if not channel:
            raise HTTPException(status_code=404, detail="Channel not found")

        # First, delete all posts associated with this channel
        if is_sqlite_db():
            # Get all posts for this channel
            posts_result = db.execute(
                select(Post).where(Post.channel_id == channel_id)
            )
            posts = posts_result.scalars().all()

            # Delete each post
            for post in posts:
                db.delete(post)

            # Now delete the channel
            db.delete(channel)
            db.commit()
        else:
            # Get all posts for this channel
            posts_result = await db.execute(
                select(Post).where(Post.channel_id == channel_id)
            )
            posts = posts_result.scalars().all()

            # Delete each post
            for post in posts:
                await db.delete(post)

            # Now delete the channel
            await db.delete(channel)
            await db.commit()

        return {"message": "Channel deleted successfully"}

# Strategy routes
@router.get("/strategies", response_model=List[StrategyModel])
async def get_strategies():
    """Get all strategies"""
    async for db in get_async_db():
        # Handle SQLite differently than other databases
        if is_sqlite_db():
            result = db.execute(select(Strategy))
        else:
            result = await db.execute(select(Strategy))
        strategies = result.scalars().all()

        return [
            StrategyModel(
                id=strategy.id,
                name=strategy.name,
                description=strategy.description,
                symbols=strategy.symbols,
                timeframes=strategy.timeframes,
                parameters=strategy.parameters,
                strategy_prompt=strategy.strategy_prompt or "",
                strategy_type=strategy.strategy_type or "general",
                code=strategy.code or "",
                active=strategy.active
            )
            for strategy in strategies
        ]

@router.post("/strategies", response_model=StrategyModel)
async def create_strategy(strategy_data: StrategyModel):
    """Create a new strategy"""
    async for db in get_async_db():
        # Create new strategy
        strategy = Strategy(
            name=strategy_data.name,
            description=strategy_data.description,
            symbols=strategy_data.symbols,
            timeframes=strategy_data.timeframes,
            parameters=strategy_data.parameters,
            strategy_prompt=strategy_data.strategy_prompt,
            strategy_type=strategy_data.strategy_type or "general",
            code=strategy_data.code or "",
            active=strategy_data.active
        )

        db.add(strategy)
        # Handle commit based on database type
        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return strategy_data

@router.put("/strategies/{strategy_id}", response_model=StrategyModel)
async def update_strategy(strategy_id: int, strategy_data: StrategyModel):
    """Update a strategy"""
    async for db in get_async_db():
        # Handle SQLite differently than other databases
        if is_sqlite_db():
            result = db.execute(
                select(Strategy).where(Strategy.id == strategy_id)
            )
        else:
            result = await db.execute(
                select(Strategy).where(Strategy.id == strategy_id)
            )
        strategy = result.scalars().first()

        if not strategy:
            raise HTTPException(status_code=404, detail="Strategy not found")

        # Update strategy
        strategy.name = strategy_data.name
        strategy.description = strategy_data.description
        strategy.symbols = strategy_data.symbols
        strategy.timeframes = strategy_data.timeframes
        strategy.parameters = strategy_data.parameters
        strategy.strategy_prompt = strategy_data.strategy_prompt
        strategy.strategy_type = strategy_data.strategy_type or "general"
        strategy.code = strategy_data.code or ""
        strategy.active = strategy_data.active
        strategy.updated_at = datetime.now(timezone.utc)

        # Handle commit based on database type
        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return strategy_data

@router.delete("/strategies/{strategy_id}")
async def delete_strategy(strategy_id: int):
    """Delete a strategy"""
    async for db in get_async_db():
        # Handle SQLite differently than other databases
        if is_sqlite_db():
            result = db.execute(
                select(Strategy).where(Strategy.id == strategy_id)
            )
        else:
            result = await db.execute(
                select(Strategy).where(Strategy.id == strategy_id)
            )
        strategy = result.scalars().first()

        if not strategy:
            raise HTTPException(status_code=404, detail="Strategy not found")

        # First, get all signals associated with this strategy
        if is_sqlite_db():
            signals_result = db.execute(
                select(Signal).where(Signal.strategy_id == strategy_id)
            )
            signals = signals_result.scalars().all()

            # For each signal, delete associated posts
            for signal in signals:
                posts_result = db.execute(
                    select(Post).where(Post.signal_id == signal.id)
                )
                posts = posts_result.scalars().all()

                for post in posts:
                    db.delete(post)

                # Delete the signal
                db.delete(signal)

            # Now delete the strategy
            db.delete(strategy)
            db.commit()
        else:
            signals_result = await db.execute(
                select(Signal).where(Signal.strategy_id == strategy_id)
            )
            signals = signals_result.scalars().all()

            # For each signal, delete associated posts
            for signal in signals:
                posts_result = await db.execute(
                    select(Post).where(Post.signal_id == signal.id)
                )
                posts = posts_result.scalars().all()

                for post in posts:
                    await db.delete(post)

                # Delete the signal
                await db.delete(signal)

            # Now delete the strategy
            await db.delete(strategy)
            await db.commit()

        return {"message": "Strategy deleted successfully"}

# Post routes
@router.get("/posts")
async def get_posts(limit: int = 50, offset: int = 0):
    """Get posts with pagination"""
    async for db in get_async_db():
        # Get total count
        if is_sqlite_db():
            count_result = db.execute(select(func.count(Post.id)))
        else:
            count_result = await db.execute(select(func.count(Post.id)))
        total_count = count_result.scalar()

        # Get posts with pagination
        if is_sqlite_db():
            result = db.execute(
                select(Post)
                .order_by(Post.created_at.desc())
                .limit(limit)
                .offset(offset)
            )
        else:
            result = await db.execute(
                select(Post)
                .order_by(Post.created_at.desc())
                .limit(limit)
                .offset(offset)
            )
        posts = result.scalars().all()

        # Import timezone helpers
        from src.utils.helpers import format_datetime_for_frontend

        return {
            "total": total_count,
            "posts": [
                {
                    "id": post.id,
                    "channel_id": post.channel_id,
                    "type": post.type.value,
                    "content": post.content,
                    "status": post.status.value,
                    "scheduled_time": post.scheduled_time.isoformat() if post.scheduled_time else None,
                    "published_time": post.published_time.isoformat() if post.published_time else None,
                    "news_id": post.news_id,
                    "event_id": post.event_id,
                    "signal_id": post.signal_id,
                    "created_at": post.created_at.isoformat() if post.created_at else None
                }
                for post in posts
            ]
        }

@router.post("/posts", response_model=PostModel)
async def create_post(post_data: PostModel):
    """Create a new post"""
    from src.utils.helpers import parse_datetime_from_frontend

    async for db in get_async_db():
        # Process scheduled_time if provided
        scheduled_time = None
        if post_data.scheduled_time:
            scheduled_time = parse_datetime_from_frontend(post_data.scheduled_time)
            if scheduled_time is None:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid scheduled_time format. Expected format: YYYY-MM-DDTHH:MM"
                )

        # Create new post
        post = Post(
            channel_id=post_data.channel_id,
            type=PostType(post_data.type),
            content=post_data.content,
            status=PostStatus(post_data.status),
            scheduled_time=scheduled_time,
            news_id=post_data.news_id,
            event_id=post_data.event_id,
            signal_id=post_data.signal_id
        )

        db.add(post)
        # Handle commit based on database type
        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return post_data

@router.delete("/posts/{post_id}")
async def delete_post(post_id: int):
    """Delete a post"""
    async for db in get_async_db():
        # Handle SQLite differently than other databases
        if is_sqlite_db():
            result = db.execute(
                select(Post).where(Post.id == post_id)
            )
        else:
            result = await db.execute(
                select(Post).where(Post.id == post_id)
            )
        post = result.scalars().first()

        if not post:
            raise HTTPException(status_code=404, detail="Post not found")

        # Handle delete and commit based on database type
        if is_sqlite_db():
            db.delete(post)
            db.commit()
        else:
            await db.delete(post)
            await db.commit()

        return {"message": "Post deleted successfully"}

@router.get("/posts/{post_id}")
async def get_post(post_id: int):
    """Get a specific post"""
    async for db in get_async_db():
        # Handle SQLite differently than other databases
        if is_sqlite_db():
            result = db.execute(
                select(Post).where(Post.id == post_id)
            )
        else:
            result = await db.execute(
                select(Post).where(Post.id == post_id)
            )
        post = result.scalars().first()

        if not post:
            raise HTTPException(status_code=404, detail="Post not found")

        return {
            "id": post.id,
            "channel_id": post.channel_id,
            "type": post.type.value,
            "content": post.content,
            "status": post.status.value,
            "scheduled_time": post.scheduled_time.isoformat() if post.scheduled_time else None,
            "published_time": post.published_time.isoformat() if post.published_time else None,
            "news_id": post.news_id,
            "event_id": post.event_id,
            "signal_id": post.signal_id,
            "created_at": post.created_at.isoformat() if post.created_at else None
        }

@router.post("/posts/{post_id}/send")
async def send_post_now(post_id: int):
    """Send a post immediately to Telegram"""
    from src.telegram.bot import send_message_to_channel
    from src.utils.helpers import get_utc_time

    async for db in get_async_db():
        # Get the post with channel information
        if is_sqlite_db():
            result = db.execute(
                select(Post, Channel)
                .join(Channel, Post.channel_id == Channel.id)
                .where(Post.id == post_id)
            )
        else:
            result = await db.execute(
                select(Post, Channel)
                .join(Channel, Post.channel_id == Channel.id)
                .where(Post.id == post_id)
            )

        row = result.first()
        if not row:
            raise HTTPException(status_code=404, detail="Post not found")

        post, channel = row

        # Check if post can be sent
        if post.status == PostStatus.PUBLISHED:
            raise HTTPException(status_code=400, detail="Post has already been published")

        try:
            logger.info(f"📤 Sending post {post_id} to channel {channel.name} (Chat ID: {channel.chat_id})")
            logger.info(f"📝 Post content length: {len(post.content)}")
            logger.info(f"📢 Channel advertisement settings:")
            logger.info(f"   📢 enable_advertisement: {channel.enable_advertisement}")
            logger.info(f"   📝 advertisement_text: '{channel.advertisement_text}'")
            logger.info(f"   🔗 advertisement_url: '{channel.advertisement_url}'")

            # Format the message for the channel (including advertisement footer)
            from src.utils.message_formatter import format_message_for_channel
            formatted_content = format_message_for_channel(post.content, channel, parse_mode="HTML")

            logger.info(f"🎨 Message formatted, final length: {len(formatted_content)}")

            # Send the post to Telegram with proper image handling
            from telegram import Bot
            from src.telegram.bot import split_and_send_message, send_photo_with_retry

            bot = Bot(token=os.getenv('TELEGRAM_BOT_TOKEN'))
            message = None

            # Check for image URL or image path (for generated charts)
            image_source = post.image_url or post.image_path
            if image_source:
                if post.image_url:
                    logger.info(f"📸 Sending post {post_id} with image URL: {post.image_url}")
                else:
                    logger.info(f"📊 Sending post {post_id} with chart image: {post.image_path}")

                # For posts with images, check if caption is too long
                # Telegram has a 1024 character limit for photo captions
                if len(formatted_content) > 1024:
                    # First send the image with a brief caption
                    try:
                        if post.image_url:
                            # Send external image URL
                            image_message = await send_photo_with_retry(
                                bot=bot,
                                chat_id=channel.chat_id,
                                photo=post.image_url,
                                caption="📊 Image for the following analysis:",
                                timeout=60
                            )
                        else:
                            # Send local chart file
                            with open(post.image_path, 'rb') as photo_file:
                                image_message = await send_photo_with_retry(
                                    bot=bot,
                                    chat_id=channel.chat_id,
                                    photo=photo_file,
                                    caption="📊 Chart for the following signal:",
                                    timeout=60
                                )
                        logger.info(f"📸 Image sent successfully")

                        # Then send the full content as text messages
                        messages = await split_and_send_message(
                            bot=bot,
                            chat_id=channel.chat_id,
                            text=formatted_content,
                            parse_mode="HTML"
                        )

                        # Use the image message ID for reference
                        message = image_message
                        logger.info(f"✅ Sent image and {len(messages)} text message(s)")
                    except Exception as url_error:
                        logger.warning(f"❌ Error sending image URL for post {post_id}: {url_error}")
                        # Fall back to text-only post
                        messages = await split_and_send_message(
                            bot=bot,
                            chat_id=channel.chat_id,
                            text=formatted_content,
                            parse_mode="HTML"
                        )
                        message = messages[0] if messages else None
                        logger.info(f"📝 Fell back to text-only: {len(messages)} message(s)")
                else:
                    # Caption is short enough, send as normal
                    try:
                        if post.image_url:
                            # Send external image URL with caption
                            message = await send_photo_with_retry(
                                bot=bot,
                                chat_id=channel.chat_id,
                                photo=post.image_url,
                                caption=formatted_content,
                                parse_mode="HTML",
                                timeout=60
                            )
                        else:
                            # Send local chart file with caption
                            with open(post.image_path, 'rb') as photo_file:
                                message = await send_photo_with_retry(
                                    bot=bot,
                                    chat_id=channel.chat_id,
                                    photo=photo_file,
                                    caption=formatted_content,
                                    parse_mode="HTML",
                                    timeout=60
                                )
                        logger.info(f"✅ Sent photo with caption")
                    except Exception as photo_error:
                        logger.warning(f"❌ Error sending photo for post {post_id}: {photo_error}")
                        # If image fails, fall back to text-only post
                        messages = await split_and_send_message(
                            bot=bot,
                            chat_id=channel.chat_id,
                            text=formatted_content,
                            parse_mode="HTML"
                        )
                        message = messages[0] if messages else None
                        logger.info(f"📝 Fell back to text-only: {len(messages)} message(s)")
            else:
                # No image URL or path, send as text message
                logger.info(f"📝 Sending post {post_id} as text-only (no image)")
                messages = await split_and_send_message(
                    bot=bot,
                    chat_id=channel.chat_id,
                    text=formatted_content,
                    parse_mode="HTML"
                )
                message = messages[0] if messages else None
                logger.info(f"📝 Sent {len(messages)} text message(s)")

            if message:
                # Update post status
                post.status = PostStatus.PUBLISHED
                post.published_time = get_utc_time()
                post.message_id = str(message.message_id)

                if is_sqlite_db():
                    db.commit()
                else:
                    await db.commit()

                return {"message": "Post sent successfully", "message_id": message.message_id}
            else:
                raise HTTPException(status_code=500, detail="Failed to send message to Telegram")

        except Exception as e:
            # Update post status to failed
            post.status = PostStatus.FAILED

            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()

            raise HTTPException(status_code=500, detail=f"Failed to send post: {str(e)}")

@router.put("/posts/{post_id}", response_model=PostModel)
async def update_post(post_id: int, post_data: PostModel):
    """Update a post"""
    async for db in get_async_db():
        # Handle SQLite differently than other databases
        if is_sqlite_db():
            result = db.execute(
                select(Post).where(Post.id == post_id)
            )
        else:
            result = await db.execute(
                select(Post).where(Post.id == post_id)
            )
        post = result.scalars().first()

        if not post:
            raise HTTPException(status_code=404, detail="Post not found")

        # Update post fields
        post.channel_id = post_data.channel_id
        post.type = PostType(post_data.type)
        post.content = post_data.content
        post.status = PostStatus(post_data.status)

        # Convert scheduled_time string to datetime if provided
        if post_data.scheduled_time:
            from src.utils.helpers import parse_datetime_from_frontend
            scheduled_time = parse_datetime_from_frontend(post_data.scheduled_time)
            if scheduled_time is None:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid scheduled_time format. Expected format: YYYY-MM-DDTHH:MM"
                )
            post.scheduled_time = scheduled_time
        else:
            post.scheduled_time = None

        post.news_id = post_data.news_id
        post.event_id = post_data.event_id
        post.signal_id = post_data.signal_id
        post.updated_at = datetime.now(timezone.utc)

        # Handle commit based on database type
        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return post_data

# Dashboard statistics
@router.get("/stats")
async def get_stats():
    """Get dashboard statistics"""
    async for db in get_async_db():
        # Get counts
        if is_sqlite_db():
            channels_result = db.execute(select(func.count(Channel.id)))
            channels_count = channels_result.scalar()

            active_channels_result = db.execute(
                select(func.count(Channel.id)).where(Channel.active == True)
            )
            active_channels_count = active_channels_result.scalar()

            strategies_result = db.execute(select(func.count(Strategy.id)))
            strategies_count = strategies_result.scalar()

            active_strategies_result = db.execute(
                select(func.count(Strategy.id)).where(Strategy.active == True)
            )
            active_strategies_count = active_strategies_result.scalar()

            # Get post counts by status
            post_counts = {}
            for status in PostStatus:
                count_result = db.execute(
                    select(func.count(Post.id)).where(Post.status == status)
                )
                post_counts[status.value] = count_result.scalar()

            # Get signal counts by status
            signal_counts = {}
            for status in SignalStatus:
                count_result = db.execute(
                    select(func.count(Signal.id)).where(Signal.status == status)
                )
                signal_counts[status.value] = count_result.scalar()

            # Get recent posts
            recent_posts_result = db.execute(
                select(Post)
                .order_by(Post.created_at.desc())
                .limit(5)
            )
        else:
            channels_result = await db.execute(select(func.count(Channel.id)))
            channels_count = channels_result.scalar()

            active_channels_result = await db.execute(
                select(func.count(Channel.id)).where(Channel.active == True)
            )
            active_channels_count = active_channels_result.scalar()

            strategies_result = await db.execute(select(func.count(Strategy.id)))
            strategies_count = strategies_result.scalar()

            active_strategies_result = await db.execute(
                select(func.count(Strategy.id)).where(Strategy.active == True)
            )
            active_strategies_count = active_strategies_result.scalar()

            # Get post counts by status
            post_counts = {}
            for status in PostStatus:
                count_result = await db.execute(
                    select(func.count(Post.id)).where(Post.status == status)
                )
                post_counts[status.value] = count_result.scalar()

            # Get signal counts by status
            signal_counts = {}
            for status in SignalStatus:
                count_result = await db.execute(
                    select(func.count(Signal.id)).where(Signal.status == status)
                )
                signal_counts[status.value] = count_result.scalar()

            # Get recent posts
            recent_posts_result = await db.execute(
                select(Post)
                .order_by(Post.created_at.desc())
                .limit(5)
            )

        recent_posts = recent_posts_result.scalars().all()

        return {
            "channels": {
                "total": channels_count,
                "active": active_channels_count
            },
            "strategies": {
                "total": strategies_count,
                "active": active_strategies_count
            },
            "posts": post_counts,
            "signals": signal_counts,
            "recent_posts": [
                {
                    "id": post.id,
                    "type": post.type.value,
                    "status": post.status.value,
                    "created_at": post.created_at
                }
                for post in recent_posts
            ]
        }

@router.get("/data-collection-status")
async def get_data_collection_status():
    """Get data collection status and countdown"""
    try:
        # Data collection runs every 6 hours (21600 seconds)
        collection_interval = 6 * 3600  # 6 hours in seconds

        # Get the last data collection time from the most recent market data
        async for db in get_async_db():
            # Get the most recent data collection timestamp from multiple sources
            from src.database.models import CandleData, NewsItem, EconomicEvent

            last_data = None

            if is_sqlite_db():
                # Check candle data
                candle_result = db.execute(
                    select(CandleData.created_at)
                    .order_by(CandleData.created_at.desc())
                    .limit(1)
                )
                candle_time = candle_result.scalar()

                # Check news items
                news_result = db.execute(
                    select(NewsItem.created_at)
                    .order_by(NewsItem.created_at.desc())
                    .limit(1)
                )
                news_time = news_result.scalar()

                # Check economic events
                event_result = db.execute(
                    select(EconomicEvent.created_at)
                    .order_by(EconomicEvent.created_at.desc())
                    .limit(1)
                )
                event_time = event_result.scalar()
            else:
                # Check candle data
                candle_result = await db.execute(
                    select(CandleData.created_at)
                    .order_by(CandleData.created_at.desc())
                    .limit(1)
                )
                candle_time = candle_result.scalar()

                # Check news items
                news_result = await db.execute(
                    select(NewsItem.created_at)
                    .order_by(NewsItem.created_at.desc())
                    .limit(1)
                )
                news_time = news_result.scalar()

                # Check economic events
                event_result = await db.execute(
                    select(EconomicEvent.created_at)
                    .order_by(EconomicEvent.created_at.desc())
                    .limit(1)
                )
                event_time = event_result.scalar()

            # Find the most recent timestamp among all data sources
            timestamps = [t for t in [candle_time, news_time, event_time] if t is not None]
            if timestamps:
                last_data = max(timestamps)

            if last_data:
                # Calculate time since last collection
                now = datetime.now(timezone.utc)

                # Ensure last_data is timezone-aware for comparison
                if last_data.tzinfo is None:
                    last_data = last_data.replace(tzinfo=timezone.utc)
                else:
                    last_data = last_data.astimezone(timezone.utc)

                time_since_last = (now - last_data).total_seconds()
                time_until_next = collection_interval - (time_since_last % collection_interval)

                # Convert to hours, minutes, seconds
                hours = int(time_until_next // 3600)
                minutes = int((time_until_next % 3600) // 60)
                seconds = int(time_until_next % 60)

                return {
                    "last_collection": last_data.isoformat(),
                    "next_collection_in_seconds": int(time_until_next),
                    "next_collection_formatted": f"{hours:02d}:{minutes:02d}:{seconds:02d}",
                    "collection_interval_hours": 6
                }
            else:
                return {
                    "last_collection": None,
                    "next_collection_in_seconds": 0,
                    "next_collection_formatted": "No data available",
                    "collection_interval_hours": 6
                }
    except Exception as e:
        logger.error(f"Error getting data collection status: {e}", exc_info=True)
        return {
            "last_collection": None,
            "next_collection_in_seconds": 0,
            "next_collection_formatted": "Error calculating",
            "collection_interval_hours": 6
        }

# News routes
@router.get("/news")
async def get_news(search: str = "", source: str = "", symbol: str = "", page: int = 1, page_size: int = 20):
    """Get news items with filtering and pagination"""
    async for db in get_async_db():
        # Build query with filters
        query = select(NewsItem)

        if search:
            query = query.where(NewsItem.title.contains(search) | NewsItem.content.contains(search))

        if source:
            query = query.where(NewsItem.source == source)

        if symbol:
            query = query.where(NewsItem.symbols.contains(symbol))

        # Get total count with filters
        count_query = select(func.count()).select_from(query.subquery())

        if is_sqlite_db():
            count_result = db.execute(count_query)
            total_count = count_result.scalar()

            # Get paginated results
            result = db.execute(
                query.order_by(NewsItem.published_at.desc())
                .limit(page_size)
                .offset((page - 1) * page_size)
            )
        else:
            count_result = await db.execute(count_query)
            total_count = count_result.scalar()

            # Get paginated results
            result = await db.execute(
                query.order_by(NewsItem.published_at.desc())
                .limit(page_size)
                .offset((page - 1) * page_size)
            )

        news_items = result.scalars().all()

        return {
            "total": total_count,
            "page": page,
            "page_size": page_size,
            "news": [
                {
                    "id": item.id,
                    "title": item.title,
                    "source": item.source,
                    "url": item.url,
                    "published_time": item.published_at,
                    "symbols": item.symbols,
                    "ai_analysis": item.ai_analysis,
                    "created_at": item.created_at
                }
                for item in news_items
            ]
        }

@router.get("/news/{news_id}")
async def get_news_item(news_id: int):
    """Get a specific news item"""
    async for db in get_async_db():
        if is_sqlite_db():
            result = db.execute(
                select(NewsItem).where(NewsItem.id == news_id)
            )
        else:
            result = await db.execute(
                select(NewsItem).where(NewsItem.id == news_id)
            )

        news_item = result.scalars().first()

        if not news_item:
            raise HTTPException(status_code=404, detail="News item not found")

        return {
            "id": news_item.id,
            "title": news_item.title,
            "content": news_item.content,
            "source": news_item.source,
            "url": news_item.url,
            "published_time": news_item.published_at,
            "symbols": news_item.symbols,
            "ai_analysis": news_item.ai_analysis,
            "created_at": news_item.created_at
        }

@router.post("/news/generate-post")
async def generate_news_post(request: dict):
    """Generate a post from a news item"""
    news_id = request.get("news_id")
    channel_id = request.get("channel_id")
    if not news_id:
        raise HTTPException(status_code=400, detail="news_id is required")

    async for db in get_async_db():
        # Get the news item
        if is_sqlite_db():
            news_result = db.execute(
                select(NewsItem).where(NewsItem.id == news_id)
            )
        else:
            news_result = await db.execute(
                select(NewsItem).where(NewsItem.id == news_id)
            )

        news_item = news_result.scalars().first()

        if not news_item:
            raise HTTPException(status_code=404, detail="News item not found")

        # Get the specified channel or first active channel
        if channel_id:
            if is_sqlite_db():
                channel_result = db.execute(
                    select(Channel).where(Channel.id == channel_id, Channel.active == True)
                )
            else:
                channel_result = await db.execute(
                    select(Channel).where(Channel.id == channel_id, Channel.active == True)
                )
        else:
            if is_sqlite_db():
                channel_result = db.execute(
                    select(Channel).where(Channel.active == True).limit(1)
                )
            else:
                channel_result = await db.execute(
                    select(Channel).where(Channel.active == True).limit(1)
                )

        channel = channel_result.scalars().first()

        if not channel:
            if channel_id:
                raise HTTPException(status_code=404, detail=f"Channel {channel_id} not found or not active")
            else:
                raise HTTPException(status_code=404, detail="No active channel found")

        # Always generate fresh AI analysis tailored for this specific channel
        # This ensures each channel gets content in their language and brand style
        try:
            # Initialize Qwen client
            qwen_client = QwenClient()

            # Generate analysis specifically for this channel
            channel_specific_content = await qwen_client.analyze_news(
                news_item,
                language=channel.language,
                channel_brand=channel.brand_name
            )

            # Ensure content is not None or empty
            if channel_specific_content and channel_specific_content.strip():
                logger.info(f"Generated channel-specific AI analysis for news item {news_id} in {channel.language} language for {channel.brand_name}")
                content = channel_specific_content
            else:
                logger.warning(f"AI analysis returned empty content for news item {news_id}, using fallback")
                raise ValueError("Empty AI content returned")
        except Exception as e:
            logger.error(f"Error generating AI analysis for news item {news_id}: {e}", exc_info=True)
            # Fallback to basic template if AI analysis fails
            content = f"📰 <b>{news_item.title}</b>\n\n"
            content += f"{news_item.content}\n\n"
            content += f"🔗 Source: {news_item.source}\n"
            content += f"📅 Published: {news_item.published_at.strftime('%Y-%m-%d %H:%M')}"

        post = Post(
            channel_id=channel.id,
            type=PostType.NEWS,
            content=content,
            status=PostStatus.DRAFT,
            news_id=news_item.id
        )

        db.add(post)

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return {"message": "Post generated successfully", "post_id": post.id}

# Events routes
@router.get("/events")
async def get_events(search: str = "", country: str = "", impact: str = "", page: int = 1, page_size: int = 20):
    """Get economic events with filtering and pagination"""
    async for db in get_async_db():
        # Build query with filters
        query = select(EconomicEvent)

        if search:
            query = query.where(EconomicEvent.title.contains(search))

        if country:
            query = query.where(EconomicEvent.country == country)

        if impact:
            query = query.where(EconomicEvent.impact == impact)

        # Get total count with filters
        count_query = select(func.count()).select_from(query.subquery())

        if is_sqlite_db():
            count_result = db.execute(count_query)
            total_count = count_result.scalar()

            # Get paginated results
            result = db.execute(
                query.order_by(EconomicEvent.event_time.desc())
                .limit(page_size)
                .offset((page - 1) * page_size)
            )
        else:
            count_result = await db.execute(count_query)
            total_count = count_result.scalar()

            # Get paginated results
            result = await db.execute(
                query.order_by(EconomicEvent.event_time.desc())
                .limit(page_size)
                .offset((page - 1) * page_size)
            )

        events = result.scalars().all()

        return {
            "total": total_count,
            "page": page,
            "page_size": page_size,
            "events": [
                {
                    "id": event.id,
                    "title": event.title,
                    "country": event.country,
                    "currency": event.currency,
                    "impact": event.impact,
                    "event_time": event.event_time,
                    "forecast": event.forecast,
                    "previous": event.previous,
                    "actual": event.actual,
                    "created_at": event.created_at
                }
                for event in events
            ]
        }

@router.get("/events/{event_id}")
async def get_event(event_id: int):
    """Get a specific economic event"""
    async for db in get_async_db():
        if is_sqlite_db():
            result = db.execute(
                select(EconomicEvent).where(EconomicEvent.id == event_id)
            )
        else:
            result = await db.execute(
                select(EconomicEvent).where(EconomicEvent.id == event_id)
            )

        event = result.scalars().first()

        if not event:
            raise HTTPException(status_code=404, detail="Economic event not found")

        return {
            "id": event.id,
            "title": event.title,
            "country": event.country,
            "currency": event.currency,
            "impact": event.impact,
            "event_time": event.event_time,
            "forecast": event.forecast,
            "previous": event.previous,
            "actual": event.actual,
            "ai_analysis": event.ai_analysis,
            "created_at": event.created_at
        }

@router.delete("/events/{event_id}")
async def delete_event(event_id: int):
    """Delete an economic event"""
    async for db in get_async_db():
        # Handle SQLite differently than other databases
        if is_sqlite_db():
            result = db.execute(
                select(EconomicEvent).where(EconomicEvent.id == event_id)
            )
        else:
            result = await db.execute(
                select(EconomicEvent).where(EconomicEvent.id == event_id)
            )
        event = result.scalars().first()

        if not event:
            raise HTTPException(status_code=404, detail="Economic event not found")

        # First, delete all posts associated with this event
        if is_sqlite_db():
            # Get all posts for this event
            posts_result = db.execute(
                select(Post).where(Post.event_id == event_id)
            )
            posts = posts_result.scalars().all()

            # Delete each post
            for post in posts:
                db.delete(post)

            # Now delete the event
            db.delete(event)
            db.commit()
        else:
            # Get all posts for this event
            posts_result = await db.execute(
                select(Post).where(Post.event_id == event_id)
            )
            posts = posts_result.scalars().all()

            # Delete each post
            for post in posts:
                await db.delete(post)

            # Now delete the event
            await db.delete(event)
            await db.commit()

        return {"message": "Economic event deleted successfully"}

@router.post("/events/generate-post")
async def generate_event_post(request: dict):
    """Generate a post from an economic event"""
    event_id = request.get("event_id")
    channel_id = request.get("channel_id")
    if not event_id:
        raise HTTPException(status_code=400, detail="event_id is required")

    async for db in get_async_db():
        # Get the event
        if is_sqlite_db():
            event_result = db.execute(
                select(EconomicEvent).where(EconomicEvent.id == event_id)
            )
        else:
            event_result = await db.execute(
                select(EconomicEvent).where(EconomicEvent.id == event_id)
            )

        event = event_result.scalars().first()

        if not event:
            raise HTTPException(status_code=404, detail="Economic event not found")

        # Get the specified channel or first active channel
        if channel_id:
            if is_sqlite_db():
                channel_result = db.execute(
                    select(Channel).where(Channel.id == channel_id, Channel.active == True)
                )
            else:
                channel_result = await db.execute(
                    select(Channel).where(Channel.id == channel_id, Channel.active == True)
                )
        else:
            if is_sqlite_db():
                channel_result = db.execute(
                    select(Channel).where(Channel.active == True).limit(1)
                )
            else:
                channel_result = await db.execute(
                    select(Channel).where(Channel.active == True).limit(1)
                )

        channel = channel_result.scalars().first()

        if not channel:
            if channel_id:
                raise HTTPException(status_code=404, detail=f"Channel {channel_id} not found or not active")
            else:
                raise HTTPException(status_code=404, detail="No active channel found")

        # Always generate fresh AI analysis tailored for this specific channel
        # This ensures each channel gets content in their language and brand style
        try:
            # Initialize Qwen client
            qwen_client = QwenClient()

            # Generate analysis specifically for this channel
            channel_specific_content = await qwen_client.analyze_economic_event(
                event,
                language=channel.language,
                channel_brand=channel.brand_name
            )

            logger.info(f"Generated channel-specific AI analysis for event {event_id} in {channel.language} language for {channel.brand_name}")
            content = channel_specific_content
        except Exception as e:
            logger.error(f"Error generating AI analysis for event {event_id}: {e}", exc_info=True)
            # Fallback to basic template if AI analysis fails
            impact_emoji = "🟢"
            if event.impact == "medium":
                impact_emoji = "🟡"
            elif event.impact == "high":
                impact_emoji = "🔴"

            content = f"📊 <b>Economic Event: {event.title}</b> {impact_emoji}\n\n"
            content += f"🌍 Country: {event.country}\n"
            content += f"💱 Currency: {event.currency}\n"
            content += f"⏰ Time: {event.event_time.strftime('%Y-%m-%d %H:%M')}\n\n"

            if event.actual:
                content += f"📈 Actual: {event.actual}\n"
            content += f"🔮 Forecast: {event.forecast or 'N/A'}\n"
            content += f"⏮️ Previous: {event.previous or 'N/A'}\n\n"
            content += "Stay tuned for market analysis after the event!"

        post = Post(
            channel_id=channel.id,
            type=PostType.EVENT,
            content=content,
            status=PostStatus.DRAFT,
            event_id=event.id
        )

        db.add(post)

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return {"message": "Post generated successfully", "post_id": post.id}

@router.post("/events/generate-countdown")
async def generate_event_countdown(request: dict):
    """Generate a countdown post for an economic event"""
    event_id = request.get("event_id")
    channel_id = request.get("channel_id")
    if not event_id:
        raise HTTPException(status_code=400, detail="event_id is required")

    async for db in get_async_db():
        # Get the event
        if is_sqlite_db():
            event_result = db.execute(
                select(EconomicEvent).where(EconomicEvent.id == event_id)
            )
        else:
            event_result = await db.execute(
                select(EconomicEvent).where(EconomicEvent.id == event_id)
            )

        event = event_result.scalars().first()

        if not event:
            raise HTTPException(status_code=404, detail="Economic event not found")

        # Get the specified channel or first active channel
        if channel_id:
            if is_sqlite_db():
                channel_result = db.execute(
                    select(Channel).where(Channel.id == channel_id, Channel.active == True)
                )
            else:
                channel_result = await db.execute(
                    select(Channel).where(Channel.id == channel_id, Channel.active == True)
                )
        else:
            if is_sqlite_db():
                channel_result = db.execute(
                    select(Channel).where(Channel.active == True).limit(1)
                )
            else:
                channel_result = await db.execute(
                    select(Channel).where(Channel.active == True).limit(1)
                )

        channel = channel_result.scalars().first()

        if not channel:
            if channel_id:
                raise HTTPException(status_code=404, detail=f"Channel {channel_id} not found or not active")
            else:
                raise HTTPException(status_code=404, detail="No active channel found")

        # Generate countdown content using Qwen AI
        try:
            # Initialize Qwen client
            qwen_client = QwenClient()

            # Generate countdown in the channel's language
            countdown_content = await qwen_client.generate_countdown_post(
                event,
                language=channel.language,
                channel_brand=channel.brand_name
            )

            logger.info(f"Generated countdown for event {event_id} in {channel.language} language")
        except Exception as e:
            logger.error(f"Error generating countdown for event {event_id}: {e}", exc_info=True)

            # Fallback to basic countdown if AI generation fails
            impact_emoji = "🟢"
            if event.impact == "medium":
                impact_emoji = "🟡"
            elif event.impact == "high":
                impact_emoji = "🔴"

            # Format event time properly with timezone information
            if event.event_time.tzinfo is None:
                # Assume stored time is UTC
                event_time_display = event.event_time.replace(tzinfo=timezone.utc)
            else:
                event_time_display = event.event_time.astimezone(timezone.utc)

            countdown_content = f"⏰ <b>Upcoming Economic Event: {event.title}</b> {impact_emoji}\n\n"
            countdown_content += f"🌍 Country: {event.country}\n"
            countdown_content += f"💱 Currency: {event.currency}\n"
            countdown_content += f"⏰ Time: {event_time_display.strftime('%Y-%m-%d %H:%M UTC')}\n\n"
            countdown_content += f"🔮 Forecast: {event.forecast or 'N/A'}\n"
            countdown_content += f"⏮️ Previous: {event.previous or 'N/A'}\n\n"
            countdown_content += "Stay tuned for updates after the event!"

        post = Post(
            channel_id=channel.id,
            type=PostType.EVENT,
            content=countdown_content,
            status=PostStatus.DRAFT,
            event_id=event.id
        )

        db.add(post)

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return {"message": "Countdown post generated successfully", "post_id": post.id}

# Signals routes
@router.get("/signals")
async def get_signals(status: str = "", symbol: str = "", strategy_id: str = "", direction: str = "", page: int = 1, page_size: int = 20):
    """Get trading signals with filtering and pagination"""
    logger.info(f"🔍 Getting signals with filters: status={status}, symbol={symbol}, strategy_id={strategy_id}, direction={direction}, page={page}, page_size={page_size}")

    async for db in get_async_db():
        # Build query with filters
        query = select(Signal)
        logger.info(f"📊 Base query created")

        if status:
            try:
                query = query.where(Signal.status == SignalStatus(status))
                logger.info(f"🔍 Added status filter: {status}")
            except ValueError:
                logger.warning(f"⚠️ Invalid status filter: {status}")

        if symbol:
            query = query.where(Signal.symbol == symbol)
            logger.info(f"🔍 Added symbol filter: {symbol}")

        if strategy_id:
            try:
                query = query.where(Signal.strategy_id == int(strategy_id))
                logger.info(f"🔍 Added strategy_id filter: {strategy_id}")
            except ValueError:
                logger.warning(f"⚠️ Invalid strategy_id filter: {strategy_id}")

        if direction:
            # Validate direction value
            if direction.lower() in ['buy', 'sell']:
                query = query.where(Signal.direction == direction.lower())
                logger.info(f"🔍 Added direction filter: {direction}")
            else:
                logger.warning(f"⚠️ Invalid direction filter: {direction}")

        # Get total count with filters
        count_query = select(func.count()).select_from(query.subquery())

        if is_sqlite_db():
            count_result = db.execute(count_query)
            total_count = count_result.scalar()

            # Get paginated results
            result = db.execute(
                query.order_by(Signal.entry_time.desc())
                .limit(page_size)
                .offset((page - 1) * page_size)
            )
        else:
            count_result = await db.execute(count_query)
            total_count = count_result.scalar()

            # Get paginated results
            result = await db.execute(
                query.order_by(Signal.entry_time.desc())
                .limit(page_size)
                .offset((page - 1) * page_size)
            )

        signals = result.scalars().all()
        logger.info(f"📊 Query executed, found {len(signals)} signals")
        logger.info(f"📈 Total count: {total_count}")

        # Log signal details for debugging
        for signal in signals:
            logger.info(f"📋 Signal {signal.id}: {signal.symbol} {signal.direction} @ {signal.entry_price} (Status: {signal.status.value})")

        # Get strategy names
        strategy_ids = [signal.strategy_id for signal in signals]
        logger.info(f"🔍 Strategy IDs found: {strategy_ids}")

        if strategy_ids:
            if is_sqlite_db():
                strategies_result = db.execute(
                    select(Strategy).where(Strategy.id.in_(strategy_ids))
                )
            else:
                strategies_result = await db.execute(
                    select(Strategy).where(Strategy.id.in_(strategy_ids))
                )

            strategies = {strategy.id: strategy.name for strategy in strategies_result.scalars().all()}
            logger.info(f"📋 Strategy names: {strategies}")
        else:
            strategies = {}
            logger.info(f"⚠️ No strategy IDs found")

        return {
            "total": total_count,
            "page": page,
            "page_size": page_size,
            "signals": [
                {
                    "id": signal.id,
                    "symbol": signal.symbol,
                    "timeframe": signal.timeframe,
                    "direction": signal.direction if signal.direction in ['buy', 'sell'] else 'Unknown',
                    "entry_price": signal.entry_price,
                    "entry_time": signal.entry_time,
                    "stop_loss": signal.stop_loss,
                    "take_profit": signal.take_profit,
                    "status": signal.status.value if hasattr(signal.status, 'value') else str(signal.status),
                    "strategy_id": signal.strategy_id,
                    "strategy_name": strategies.get(signal.strategy_id, "Unknown"),
                    "exit_price": signal.exit_price,
                    "exit_time": signal.exit_time,
                    "created_at": signal.created_at
                }
                for signal in signals
            ]
        }

@router.get("/signals/debug/count")
async def get_signals_debug_count():
    """Debug endpoint to check total signals count"""
    async for db in get_async_db():
        if is_sqlite_db():
            count_result = db.execute(select(func.count(Signal.id)))
            total_signals = count_result.scalar()

            # Get count by status
            status_counts = {}
            for status in SignalStatus:
                status_result = db.execute(
                    select(func.count(Signal.id)).where(Signal.status == status)
                )
                status_counts[status.value] = status_result.scalar()
        else:
            count_result = await db.execute(select(func.count(Signal.id)))
            total_signals = count_result.scalar()

            # Get count by status
            status_counts = {}
            for status in SignalStatus:
                status_result = await db.execute(
                    select(func.count(Signal.id)).where(Signal.status == status)
                )
                status_counts[status.value] = status_result.scalar()

        logger.info(f"🔍 Debug signals count: total={total_signals}, by_status={status_counts}")

        return {
            "total_signals": total_signals,
            "status_counts": status_counts
        }

@router.get("/signals/{signal_id}")
async def get_signal(signal_id: int):
    """Get a specific trading signal"""
    async for db in get_async_db():
        if is_sqlite_db():
            result = db.execute(
                select(Signal).where(Signal.id == signal_id)
            )

            signal = result.scalars().first()

            if signal:
                # Get strategy name
                strategy_result = db.execute(
                    select(Strategy).where(Strategy.id == signal.strategy_id)
                )
                strategy = strategy_result.scalars().first()
            else:
                strategy = None
        else:
            result = await db.execute(
                select(Signal).where(Signal.id == signal_id)
            )
            signal = result.scalars().first()

            if signal:
                # Get strategy name
                strategy_result = await db.execute(
                    select(Strategy).where(Strategy.id == signal.strategy_id)
                )
                strategy = strategy_result.scalars().first()
            else:
                strategy = None

        if not signal:
            raise HTTPException(status_code=404, detail="Trading signal not found")

        return {
            "id": signal.id,
            "symbol": signal.symbol,
            "timeframe": signal.timeframe,
            "direction": signal.direction if signal.direction in ['buy', 'sell'] else 'Unknown',
            "entry_price": signal.entry_price,
            "entry_time": signal.entry_time,
            "stop_loss": signal.stop_loss,
            "take_profit": signal.take_profit,
            "status": signal.status.value if hasattr(signal.status, 'value') else str(signal.status),
            "strategy_id": signal.strategy_id,
            "strategy_name": strategy.name if strategy else "Unknown",
            "exit_price": signal.exit_price,
            "exit_time": signal.exit_time,
            "notes": signal.notes,
            "ai_analysis": signal.ai_analysis,
            "created_at": signal.created_at
        }

@router.put("/signals/{signal_id}")
async def update_signal(signal_id: int, signal_data: dict):
    """Update a trading signal"""
    async for db in get_async_db():
        if is_sqlite_db():
            result = db.execute(
                select(Signal).where(Signal.id == signal_id)
            )
        else:
            result = await db.execute(
                select(Signal).where(Signal.id == signal_id)
            )

        signal = result.scalars().first()

        if not signal:
            raise HTTPException(status_code=404, detail="Trading signal not found")

        # Update signal fields
        if "status" in signal_data:
            signal.status = SignalStatus(signal_data["status"])

        if "exit_price" in signal_data and signal_data["exit_price"] is not None:
            signal.exit_price = float(signal_data["exit_price"])

        if "exit_time" in signal_data:
            signal.exit_time = signal_data["exit_time"]

        if "notes" in signal_data:
            signal.notes = signal_data["notes"]

        signal.updated_at = datetime.now(timezone.utc)

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return {"message": "Signal updated successfully"}

@router.delete("/signals/{signal_id}")
async def delete_signal(signal_id: int):
    """Delete a trading signal and related data"""
    async for db in get_async_db():
        # Check if signal exists
        if is_sqlite_db():
            result = db.execute(
                select(Signal).where(Signal.id == signal_id)
            )
        else:
            result = await db.execute(
                select(Signal).where(Signal.id == signal_id)
            )

        signal = result.scalars().first()

        if not signal:
            raise HTTPException(status_code=404, detail="Trading signal not found")

        try:
            # Delete related posts first (due to foreign key constraints)
            if is_sqlite_db():
                posts_result = db.execute(
                    select(Post).where(Post.signal_id == signal_id)
                )
            else:
                posts_result = await db.execute(
                    select(Post).where(Post.signal_id == signal_id)
                )

            posts = posts_result.scalars().all()
            for post in posts:
                db.delete(post)

            # Delete related take profit hits
            from src.database.models import TakeProfitHit
            if is_sqlite_db():
                tp_hits_result = db.execute(
                    select(TakeProfitHit).where(TakeProfitHit.signal_id == signal_id)
                )
            else:
                tp_hits_result = await db.execute(
                    select(TakeProfitHit).where(TakeProfitHit.signal_id == signal_id)
                )

            tp_hits = tp_hits_result.scalars().all()
            for tp_hit in tp_hits:
                db.delete(tp_hit)

            # Delete the signal itself
            db.delete(signal)

            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()

            logger.info(f"Successfully deleted signal {signal_id} and {len(posts)} related posts and {len(tp_hits)} TP hits")
            return {"message": f"Signal {signal_id} deleted successfully"}

        except Exception as e:
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
            logger.error(f"Error deleting signal {signal_id}: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Error deleting signal: {str(e)}")

@router.post("/signals/generate-post")
async def generate_signal_post(request: dict):
    """Generate a post from a trading signal"""
    signal_id = request.get("signal_id")
    channel_id = request.get("channel_id")
    if not signal_id:
        raise HTTPException(status_code=400, detail="signal_id is required")

    async for db in get_async_db():
        # Get the signal
        if is_sqlite_db():
            signal_result = db.execute(
                select(Signal).where(Signal.id == signal_id)
            )
            signal = signal_result.scalars().first()

            if signal:
                # Get strategy name
                strategy_result = db.execute(
                    select(Strategy).where(Strategy.id == signal.strategy_id)
                )
                strategy = strategy_result.scalars().first()
            else:
                strategy = None
        else:
            signal_result = await db.execute(
                select(Signal).where(Signal.id == signal_id)
            )
            signal = signal_result.scalars().first()

            if signal:
                # Get strategy name
                strategy_result = await db.execute(
                    select(Strategy).where(Strategy.id == signal.strategy_id)
                )
                strategy = strategy_result.scalars().first()
            else:
                strategy = None

        if not signal:
            raise HTTPException(status_code=404, detail="Trading signal not found")

        # Get the specified channel or first active channel
        if channel_id:
            if is_sqlite_db():
                channel_result = db.execute(
                    select(Channel).where(Channel.id == channel_id, Channel.active == True)
                )
            else:
                channel_result = await db.execute(
                    select(Channel).where(Channel.id == channel_id, Channel.active == True)
                )
        else:
            if is_sqlite_db():
                channel_result = db.execute(
                    select(Channel).where(Channel.active == True).limit(1)
                )
            else:
                channel_result = await db.execute(
                    select(Channel).where(Channel.active == True).limit(1)
                )

        channel = channel_result.scalars().first()

        if not channel:
            if channel_id:
                raise HTTPException(status_code=404, detail=f"Channel {channel_id} not found or not active")
            else:
                raise HTTPException(status_code=404, detail="No active channel found")

        # Check if signal has a chart image, if not, generate one
        if not signal.chart_image:
            try:
                from src.visualization.chart_generator import generate_signal_chart
                chart_path = await generate_signal_chart(signal)

                if chart_path:
                    logger.info(f"Chart generated successfully: {chart_path}")

                    # Update signal with chart image
                    signal.chart_image = chart_path
                    if is_sqlite_db():
                        db.commit()
                    else:
                        await db.commit()

                    logger.info(f"Signal updated with chart image: {chart_path}")
                else:
                    logger.warning(f"Failed to generate chart for signal {signal.id}")
            except Exception as chart_error:
                logger.error(f"Error generating chart for signal {signal.id}: {chart_error}", exc_info=True)

        # Generate AI analysis if not already done or if we need to regenerate in the channel's language
        if not hasattr(signal, 'ai_analysis') or not signal.ai_analysis or request.get("regenerate", False):
            try:
                # Initialize Qwen client
                qwen_client = QwenClient()

                # Generate analysis in the channel's language
                signal.ai_analysis = await qwen_client.analyze_trading_signal(
                    signal,
                    language=channel.language,
                    channel_brand=channel.brand_name
                )

                # Save the analysis
                if is_sqlite_db():
                    db.commit()
                else:
                    await db.commit()

                logger.info(f"Generated AI analysis for signal {signal_id} in {channel.language} language")
            except Exception as e:
                logger.error(f"Error generating AI analysis for signal {signal_id}: {e}", exc_info=True)

        # Generate AI-based content using the prompt template system
        try:
            from src.ai_integration.qwen_client import QwenClient
            from src.ai_integration.content_generator import ensure_channel_attributes

            # Get Qwen client
            qwen_client = QwenClient()

            # Get channel attributes safely
            safe_attrs = ensure_channel_attributes(channel)

            # Generate AI content using the prompt template
            content = await qwen_client.analyze_trading_signal(
                signal,
                language=safe_attrs['language'],
                channel_brand=safe_attrs['brand_name']
            )

            # Note: Advertisement footer will be added when the post is actually sent
            # Do not add footer during generation - it's handled by message_formatter.py

        except Exception as e:
            logger.error(f"Error generating AI signal content: {e}", exc_info=True)
            # Fallback to basic format if AI generation fails
            direction_emoji = "🔴" if signal.direction == "sell" else "🟢"
            status_emoji = "⏳"
            if signal.status == SignalStatus.TP_HIT or signal.status == SignalStatus.SL_HIT:
                status_emoji = "✅"
            elif signal.status == SignalStatus.CANCELLED:
                status_emoji = "❌"

            content = f"{direction_emoji} <b>Trading Signal: {signal.symbol} {signal.timeframe}</b> {status_emoji}\n\n"
            content += f"📈 Direction: {'SELL' if signal.direction == 'sell' else 'BUY'}\n"
            content += f"💰 Entry Price: {signal.entry_price}\n"
            content += f"⏰ Entry Time: {signal.entry_time.strftime('%Y-%m-%d %H:%M')}\n"
            content += f"🛑 Stop Loss: {signal.stop_loss}\n"

            # Handle multi-TP fallback
            if hasattr(signal, 'take_profit_1') and signal.take_profit_1:
                content += f"🎯 TP1: {signal.take_profit_1}"
                if hasattr(signal, 'take_profit_2') and signal.take_profit_2:
                    content += f"\n🎯 TP2: {signal.take_profit_2}"
                if hasattr(signal, 'take_profit_3') and signal.take_profit_3:
                    content += f"\n🎯 TP3: {signal.take_profit_3}"
                content += "\n"
            else:
                # Legacy single TP
                content += f"🎯 Take Profit: {signal.take_profit}\n"

            content += f"\n🤖 Strategy: {strategy.name if strategy else 'Unknown'}\n"

            if signal.notes:
                content += f"\n<b>Notes:</b>\n{signal.notes}"

        post = Post(
            channel_id=channel.id,
            type=PostType.SIGNAL,
            content=content,
            status=PostStatus.DRAFT,
            signal_id=signal.id,
            image_path=signal.chart_image
        )

        db.add(post)

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return {"message": "Post generated successfully", "post_id": post.id}

@router.post("/signals/generate-update")
async def generate_signal_update(request: dict):
    """Generate a performance update post for a trading signal"""
    signal_id = request.get("signal_id")
    channel_id = request.get("channel_id")
    if not signal_id:
        raise HTTPException(status_code=400, detail="signal_id is required")

    async for db in get_async_db():
        # Get the signal
        if is_sqlite_db():
            signal_result = db.execute(
                select(Signal).where(Signal.id == signal_id)
            )
            signal = signal_result.scalars().first()
        else:
            signal_result = await db.execute(
                select(Signal).where(Signal.id == signal_id)
            )
            signal = signal_result.scalars().first()

        if not signal:
            raise HTTPException(status_code=404, detail="Trading signal not found")

        # Get the specified channel or first active channel
        if channel_id:
            if is_sqlite_db():
                channel_result = db.execute(
                    select(Channel).where(Channel.id == channel_id, Channel.active == True)
                )
            else:
                channel_result = await db.execute(
                    select(Channel).where(Channel.id == channel_id, Channel.active == True)
                )
        else:
            if is_sqlite_db():
                channel_result = db.execute(
                    select(Channel).where(Channel.active == True).limit(1)
                )
            else:
                channel_result = await db.execute(
                    select(Channel).where(Channel.active == True).limit(1)
                )

        channel = channel_result.scalars().first()

        if not channel:
            if channel_id:
                raise HTTPException(status_code=404, detail=f"Channel {channel_id} not found or not active")
            else:
                raise HTTPException(status_code=404, detail="No active channel found")

        # Generate PnL image as primary format
        logger.info(f"🖼️ Generating PnL image for signal {signal_id} performance update...")

        # Import the image generator
        from src.ai_integration.content_generator import generate_pnl_image_from_signal
        pnl_image_path = generate_pnl_image_from_signal(signal, tp_level=None)

        # Generate AI text content as fallback
        update_content = None
        try:
            # Initialize Qwen client
            qwen_client = QwenClient()

            # Generate update in the channel's language
            update_content = await qwen_client.generate_signal_update(
                signal,
                language=channel.language,
                channel_brand=channel.brand_name
            )

            logger.info(f"Generated AI performance update for signal {signal_id} in {channel.language} language")
        except Exception as e:
            logger.error(f"Error generating AI performance update for signal {signal_id}: {e}", exc_info=True)

        # Import performance caption function
        from src.ai_integration.content_generator import get_performance_caption, calculate_pips

        # Use image as primary, text as fallback
        if pnl_image_path:
            logger.info(f"✅ Using PnL image for performance update: {pnl_image_path}")

            # Generate localized caption based on channel language
            channel_language = getattr(channel, 'language', 'en')
            post_content = get_performance_caption('performance_update', channel_language)

            # Add pips information for forex symbols only
            if hasattr(signal, 'symbol') and is_forex_pair(signal.symbol):
                if signal.exit_price and signal.entry_price:
                    pips = calculate_pips(signal.entry_price, signal.exit_price, signal.symbol)
                    if pips > 0:
                        # Determine if profit or loss based on direction and prices
                        is_profit = ((signal.direction == "buy" and signal.exit_price > signal.entry_price) or
                                   (signal.direction == "sell" and signal.exit_price < signal.entry_price))

                        pips_type = 'pips_profit' if is_profit else 'pips_loss'
                        pips_caption = get_performance_caption(pips_type, channel_language, pips=pips)
                        post_content += f"\n{pips_caption}"

                        logger.info(f"📊 Admin pips caption: {signal.symbol} {signal.direction} | "
                                  f"Entry: {signal.entry_price} | Exit: {signal.exit_price} | "
                                  f"Pips: {pips} | Profit: {is_profit} | Caption: {pips_caption}")

            logger.info(f"📝 Generated localized performance update caption ({channel_language}): {post_content}")

            # Store local image path in image_path field, not image_url
            image_url = None
            local_image_path = pnl_image_path
        elif update_content:
            logger.info(f"⚠️ Using AI text for performance update (image generation failed)")
            post_content = update_content
            local_image_path = None
            # Use original chart as fallback (if it's a URL)
            if signal.chart_image and signal.chart_image.startswith('http'):
                image_url = signal.chart_image
            else:
                image_url = None
        else:
            # Final fallback to basic text
            logger.warning(f"⚠️ Using basic text fallback for performance update (both image and AI failed)")
            direction = "BUY" if signal.direction == "buy" else "SELL"
            status = signal.status.value.upper()
            profit_loss = f"{signal.profit_loss:.2f}%" if signal.profit_loss is not None else "N/A"

            post_content = f"📊 <b>Signal Update: {signal.symbol} {signal.timeframe} {direction}</b>\n\n"
            post_content += f"Status: {status}\n"
            post_content += f"Entry Price: {signal.entry_price}\n"
            post_content += f"Exit Price: {signal.exit_price or 'N/A'}\n"
            post_content += f"Profit/Loss: {profit_loss}\n\n"
            post_content += "Signal has been closed."
            local_image_path = None
            # Use original chart as fallback (if it's a URL)
            if signal.chart_image and signal.chart_image.startswith('http'):
                image_url = signal.chart_image
            else:
                image_url = None

        # Find the original signal post to reply to
        reply_to_message_id = None
        logger.info(f"🔍 SEARCHING for original signal post to reply to...")
        logger.info(f"🔍 Looking for: signal_id={signal.id}, channel_id={channel.id}, type=SIGNAL, status=PUBLISHED")

        try:
            # First, try to find a signal post with message_id (preferred)
            if is_sqlite_db():
                signal_post_result = db.execute(
                    select(Post).where(
                        Post.channel_id == channel.id,
                        Post.signal_id == signal.id,
                        Post.type == PostType.SIGNAL,
                        Post.message_id.isnot(None)
                    ).order_by(Post.created_at.desc()).limit(1)
                )
            else:
                signal_post_result = await db.execute(
                    select(Post).where(
                        Post.channel_id == channel.id,
                        Post.signal_id == signal.id,
                        Post.type == PostType.SIGNAL,
                        Post.message_id.isnot(None)
                    ).order_by(Post.created_at.desc()).limit(1)
                )

            signal_post = signal_post_result.scalars().first()

            # If no post with message_id found, try to find any signal post
            if not signal_post:
                logger.info(f"🔍 No signal post with message_id found, searching for any signal post...")
                if is_sqlite_db():
                    signal_post_result = db.execute(
                        select(Post).where(
                            Post.channel_id == channel.id,
                            Post.signal_id == signal.id,
                            Post.type == PostType.SIGNAL
                        ).order_by(Post.created_at.desc()).limit(1)
                    )
                else:
                    signal_post_result = await db.execute(
                        select(Post).where(
                            Post.channel_id == channel.id,
                            Post.signal_id == signal.id,
                            Post.type == PostType.SIGNAL
                        ).order_by(Post.created_at.desc()).limit(1)
                    )
                signal_post = signal_post_result.scalars().first()

            logger.info(f"🔍 Database query result: {signal_post}")

            if signal_post:
                logger.info(f"🔍 Found signal post: ID={signal_post.id}, message_id={signal_post.message_id}, status={signal_post.status}")
                if signal_post.message_id:
                    # Use the first message ID if there are multiple (from chunked messages)
                    reply_to_message_id = signal_post.message_id.split(',')[0]
                    logger.info(f"✅ REPLY SETUP SUCCESS: Performance update will reply to message ID: {reply_to_message_id}")
                else:
                    logger.warning(f"⚠️ REPLY SETUP PARTIAL: Signal post found but no message_id - will post without reply")
                    reply_to_message_id = None
            else:
                logger.error(f"❌ REPLY SETUP FAILED: No signal post found for signal {signal.id} in channel {channel.id}")
                reply_to_message_id = None

                # Debug: Let's see what posts exist for this signal
                debug_result = db.execute(select(Post).where(Post.signal_id == signal.id)) if is_sqlite_db() else await db.execute(select(Post).where(Post.signal_id == signal.id))
                all_posts = debug_result.scalars().all()
                logger.info(f"🔍 DEBUG: All posts for signal {signal.id}: {[(p.id, p.type, p.status, p.channel_id, p.message_id) for p in all_posts]}")

        except Exception as e:
            logger.error(f"❌ REPLY SETUP ERROR: Exception finding original signal post: {e}", exc_info=True)
            reply_to_message_id = None

        logger.info(f"📝 CREATING performance update post with reply_to_message_id: {reply_to_message_id}")

        post = Post(
            channel_id=channel.id,
            type=PostType.PERFORMANCE,
            content=post_content,
            status=PostStatus.DRAFT,
            signal_id=signal.id,
            image_url=image_url,  # Use for URL-based images
            image_path=local_image_path,  # Use for local image files
            reply_to_message_id=reply_to_message_id
        )

        db.add(post)

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return {"message": "Performance update post generated successfully", "post_id": post.id}

# Wrapper functions with detailed logging
async def logged_process_strategies():
    """Process trading strategies with detailed logging"""
    logger.info("🔄 Starting strategy processing...")
    try:
        await process_strategies_task()
        logger.info("✅ Strategy processing completed successfully")
    except Exception as e:
        logger.error(f"❌ Strategy processing failed: {e}", exc_info=True)

async def logged_collect_events():
    """Collect economic events with detailed logging"""
    logger.info("🔄 Starting economic events collection...")
    try:
        await collect_events_task()
        logger.info("✅ Economic events collection completed successfully")
    except Exception as e:
        logger.error(f"❌ Economic events collection failed: {e}", exc_info=True)

async def logged_collect_news():
    """Collect news with detailed logging"""
    logger.info("🔄 Starting news collection...")
    try:
        await collect_news_task()
        logger.info("✅ News collection completed successfully")
    except Exception as e:
        logger.error(f"❌ News collection failed: {e}", exc_info=True)

async def logged_generate_content():
    """Generate content with detailed logging"""
    logger.info("🔄 Starting content generation...")
    try:
        await generate_content()
        logger.info("✅ Content generation completed successfully")
    except Exception as e:
        logger.error(f"❌ Content generation failed: {e}", exc_info=True)

async def logged_collect_data():
    """Collect data with detailed logging"""
    logger.info("🔄 Starting data collection...")
    try:
        await data_collection_task()
        logger.info("✅ Data collection completed successfully")
    except Exception as e:
        logger.error(f"❌ Data collection failed: {e}", exc_info=True)

async def logged_generate_greeting():
    """Generate greeting messages with detailed logging"""
    logger.info("👋 Starting greeting message generation...")
    try:
        from src.ai_integration.content_scheduler import generate_greeting_posts_for_all_channels
        posts_created = await generate_greeting_posts_for_all_channels()
        logger.info(f"✅ Greeting message generation completed successfully. Created {posts_created} greeting posts.")
    except Exception as e:
        logger.error(f"❌ Greeting message generation failed: {e}", exc_info=True)

@router.post("/actions/process-strategies")
async def process_strategies(background_tasks: BackgroundTasks):
    """Process trading strategies to generate signals"""
    logger.info("📊 Strategy processing requested via admin interface")
    background_tasks.add_task(logged_process_strategies)
    return {"message": "Strategy processing started"}

@router.post("/actions/collect-events")
async def collect_events(background_tasks: BackgroundTasks):
    """Collect economic events"""
    logger.info("📅 Economic events collection requested via admin interface")
    background_tasks.add_task(logged_collect_events)
    return {"message": "Economic events collection started"}

@router.post("/actions/collect-news")
async def collect_news(background_tasks: BackgroundTasks):
    """Collect news items"""
    logger.info("📰 News collection requested via admin interface")
    background_tasks.add_task(logged_collect_news)
    return {"message": "News collection started"}

# Actions
@router.post("/actions/generate-content")
async def trigger_content_generation(background_tasks: BackgroundTasks):
    """Trigger content generation using original method"""
    logger.info("🤖 Content generation requested via admin interface")
    background_tasks.add_task(logged_generate_content)
    return {"message": "Content generation started"}

@router.post("/actions/collect-data")
async def trigger_data_collection(background_tasks: BackgroundTasks):
    """Trigger data collection"""
    logger.info("📈 Data collection requested via admin interface")
    background_tasks.add_task(logged_collect_data)
    return {"message": "Data collection started"}

@router.post("/actions/generate-greeting")
async def trigger_greeting_generation(background_tasks: BackgroundTasks):
    """Trigger greeting message generation for all channels"""
    logger.info("👋 Greeting message generation requested via admin interface")
    background_tasks.add_task(logged_generate_greeting)
    return {"message": "Greeting message generation started"}

# Pydantic model for test-qwen API request
class TestQwenRequest(BaseModel):
    prompt: str

@router.post("/actions/test-qwen")
async def test_qwen_api(request: TestQwenRequest):
    """Test Qwen API"""
    try:
        qwen_client = QwenClient()
        response = await qwen_client.generate_content(request.prompt, max_tokens=500)
        return {"response": response}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/actions/remove-all-posts")
async def remove_all_posts():
    """Remove all posts from the database"""
    try:
        logger.info("🗑️ Removing all posts requested via admin interface")

        async for db in get_async_db():
            if is_sqlite_db():
                # For SQLite, use synchronous operations
                result = db.execute(select(func.count(Post.id)))
                count_before = result.scalar()

                db.execute(delete(Post))
                db.commit()
            else:
                # For async databases
                result = await db.execute(select(func.count(Post.id)))
                count_before = result.scalar()

                await db.execute(delete(Post))
                await db.commit()

            logger.info(f"✅ Successfully removed {count_before} posts")
            return {"message": f"Successfully removed {count_before} posts"}

    except Exception as e:
        logger.error(f"❌ Error removing all posts: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/actions/remove-all-signals")
async def remove_all_signals():
    """Remove all signals from the database"""
    try:
        logger.info("🗑️ Removing all signals requested via admin interface")

        async for db in get_async_db():
            if is_sqlite_db():
                # For SQLite, use synchronous operations
                result = db.execute(select(func.count(Signal.id)))
                count_before = result.scalar()

                db.execute(delete(Signal))
                db.commit()
            else:
                # For async databases
                result = await db.execute(select(func.count(Signal.id)))
                count_before = result.scalar()

                await db.execute(delete(Signal))
                await db.commit()

            logger.info(f"✅ Successfully removed {count_before} signals")
            return {"message": f"Successfully removed {count_before} signals"}

    except Exception as e:
        logger.error(f"❌ Error removing all signals: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/actions/force-greeting")
async def force_greeting_generation(background_tasks: BackgroundTasks):
    """Force greeting message generation for all channels (ignores daily limit)"""
    logger.info("👋 Force greeting message generation requested via admin interface")

    async def logged_force_greeting():
        """Force generate greeting messages with detailed logging"""
        logger.info("👋 Starting FORCED greeting message generation (ignoring daily limit)...")
        try:
            from src.ai_integration.content_generator import generate_greeting_posts
            from src.ai_integration.qwen_client import QwenClient

            posts_created = 0

            async for db in get_async_db():
                # Get all active channels
                if is_sqlite_db():
                    result = db.execute(select(Channel).where(Channel.active == True))
                else:
                    result = await db.execute(select(Channel).where(Channel.active == True))
                channels = result.scalars().all()

                # Generate ONE sticker for ALL channels (if any channel has stickers enabled)
                sticker_path = None
                sticker_enabled_channels = [ch for ch in channels if getattr(ch, 'enable_date_stickers', True)]

                if sticker_enabled_channels:
                    logger.info(f"🎨 FORCE generating ONE date sticker for {len(sticker_enabled_channels)} channels...")
                    from src.image_generation.date_sticker_generator import DateStickerGenerator
                    sticker_generator = DateStickerGenerator()
                    sticker_path = sticker_generator.generate_date_sticker()

                    if sticker_path:
                        logger.info(f"✅ FORCE global date sticker generated: {sticker_path}")
                    else:
                        logger.warning(f"⚠️ FORCE failed to generate global date sticker")

                qwen_client = QwenClient()

                for channel in channels:
                    try:
                        logger.info(f"🎯 FORCE generating greeting for channel: {channel.name}")
                        created = await generate_greeting_posts(db, channel, qwen_client, force=True, global_sticker_path=sticker_path)
                        posts_created += created
                        logger.info(f"✅ FORCE created {created} greeting posts for channel {channel.name}")
                    except Exception as e:
                        logger.error(f"❌ FORCE greeting generation failed for channel {channel.name}: {e}", exc_info=True)
                        continue

            logger.info(f"✅ FORCED greeting message generation completed successfully. Created {posts_created} greeting posts.")
        except Exception as e:
            logger.error(f"❌ FORCED greeting message generation failed: {e}", exc_info=True)

    background_tasks.add_task(logged_force_greeting)
    return {"message": "Force greeting message generation started"}

@router.get("/content-scheduler/status")
async def get_content_scheduler_status():
    """Get greeting scheduler status"""
    try:
        from src.ai_integration.content_scheduler import get_next_greeting_generation_time, get_time_until_next_generation

        next_generation = get_next_greeting_generation_time()
        time_until = get_time_until_next_generation()

        return {
            "next_generation": next_generation.strftime('%Y-%m-%d %H:%M:%S %Z'),
            "time_until_hours": time_until.total_seconds() / 3600,
            "time_until_formatted": f"{int(time_until.total_seconds() // 3600)}h {int((time_until.total_seconds() % 3600) // 60)}m",
            "timezone": "Asia/Tehran"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/weekly-performance-scheduler/status")
async def get_weekly_performance_scheduler_status():
    """Get weekly performance scheduler status"""
    try:
        from src.ai_integration.weekly_performance_scheduler import get_next_weekly_report_time, get_time_until_next_weekly_report

        next_report = get_next_weekly_report_time()
        time_until = get_time_until_next_weekly_report()

        return {
            "next_report": next_report.strftime('%Y-%m-%d %H:%M:%S %Z'),
            "time_until_hours": time_until.total_seconds() / 3600,
            "time_until_formatted": f"{int(time_until.total_seconds() // 3600)}h {int((time_until.total_seconds() % 3600) // 60)}m",
            "day_of_week": "Saturday",
            "time_of_day": "10:00 AM",
            "timezone": get_timezone().zone
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/actions/generate-weekly-performance")
async def trigger_weekly_performance_reports(background_tasks: BackgroundTasks):
    """Generate and send weekly performance reports for all channels"""
    logger.info("📊 Weekly performance report generation requested via admin interface")

    async def logged_generate_weekly_performance():
        """Generate weekly performance reports with detailed logging"""
        try:
            from src.ai_integration.weekly_performance_scheduler import generate_weekly_performance_reports
            reports_sent = await generate_weekly_performance_reports()
            logger.info(f"Weekly performance reports sent: {reports_sent}")
            return reports_sent
        except Exception as e:
            logger.error(f"Error generating weekly performance reports: {e}", exc_info=True)
            return 0

    background_tasks.add_task(logged_generate_weekly_performance)
    return {"message": "Weekly performance report generation started"}

@router.get("/monthly-performance-scheduler/status")
async def get_monthly_performance_scheduler_status():
    """Get monthly performance scheduler status"""
    try:
        from src.ai_integration.monthly_performance_scheduler import get_next_monthly_report_time, get_time_until_next_monthly_report

        next_report = get_next_monthly_report_time()
        time_until = get_time_until_next_monthly_report()

        return {
            "next_report": next_report.strftime('%Y-%m-%d %H:%M:%S %Z'),
            "time_until_hours": time_until.total_seconds() / 3600,
            "time_until_formatted": f"{int(time_until.total_seconds() // 3600)}h {int((time_until.total_seconds() % 3600) // 60)}m",
            "day_of_week": "1st of month",
            "time_of_day": "11:00 AM",
            "timezone": get_timezone().zone
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/actions/generate-monthly-performance")
async def trigger_monthly_performance_reports(background_tasks: BackgroundTasks):
    """Generate and send monthly performance reports for all channels"""
    logger.info("📊 Monthly performance report generation requested via admin interface")

    async def logged_generate_monthly_performance():
        """Generate monthly performance reports with detailed logging"""
        try:
            from src.ai_integration.monthly_performance_scheduler import generate_monthly_performance_reports
            reports_sent = await generate_monthly_performance_reports()
            logger.info(f"Monthly performance reports sent: {reports_sent}")
            return reports_sent
        except Exception as e:
            logger.error(f"Error generating monthly performance reports: {e}", exc_info=True)
            return 0

    background_tasks.add_task(logged_generate_monthly_performance)
    return {"message": "Monthly performance report generation started"}

# Database cleanup routes
@router.get("/cleanup/duplicates/count")
async def get_duplicate_events_count_endpoint():
    """Get the count of duplicate events"""
    try:
        count = await get_duplicate_events_count()
        return {
            "duplicate_count": count,
            "message": f"Found {count} duplicate events"
        }
    except Exception as e:
        logger.error(f"Error counting duplicate events: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error counting duplicates: {str(e)}")

@router.post("/cleanup/duplicates")
async def cleanup_duplicate_events():
    """Delete duplicate events from the database"""
    try:
        deleted_count = await delete_duplicate_events()
        return {
            "deleted_count": deleted_count,
            "message": f"Successfully deleted {deleted_count} duplicate events"
        }
    except Exception as e:
        logger.error(f"Error cleaning up duplicate events: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error cleaning up duplicates: {str(e)}")

@router.post("/cleanup/old-events")
async def cleanup_old_events_endpoint(days_to_keep: int = 30):
    """Delete events older than specified days"""
    try:
        if days_to_keep < 1:
            raise HTTPException(status_code=400, detail="days_to_keep must be at least 1")

        deleted_count = await cleanup_old_events(days_to_keep)
        return {
            "deleted_count": deleted_count,
            "days_to_keep": days_to_keep,
            "message": f"Successfully deleted {deleted_count} events older than {days_to_keep} days"
        }
    except Exception as e:
        logger.error(f"Error cleaning up old events: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error cleaning up old events: {str(e)}")

# Prompt Template routes
@router.get("/prompt-templates", response_model=List[PromptTemplateModel])
async def get_prompt_templates():
    """Get all prompt templates"""
    try:
        if is_sqlite_db():
            # For SQLite, use synchronous session
            from src.database.setup import SessionLocal
            with SessionLocal() as db:
                result = db.execute(select(PromptTemplate).order_by(PromptTemplate.post_type, PromptTemplate.language))
                templates = result.scalars().all()

                return [
                    PromptTemplateModel(
                        id=template.id,
                        post_type=template.post_type,
                        language=template.language,
                        template_content=template.template_content,
                        description=template.description,
                        active=template.active
                    )
                    for template in templates
                ]
        else:
            # For async databases
            async for db in get_async_db():
                result = await db.execute(select(PromptTemplate).order_by(PromptTemplate.post_type, PromptTemplate.language))
                templates = result.scalars().all()

                return [
                    PromptTemplateModel(
                        id=template.id,
                        post_type=template.post_type,
                        language=template.language,
                        template_content=template.template_content,
                        description=template.description,
                        active=template.active
                    )
                    for template in templates
                ]
    except Exception as e:
        logger.error(f"Error fetching prompt templates: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching prompt templates: {str(e)}")

@router.get("/prompt-templates/{template_id}", response_model=PromptTemplateModel)
async def get_prompt_template(template_id: int):
    """Get a specific prompt template"""
    try:
        async for db in get_async_db():
            if is_sqlite_db():
                result = db.execute(select(PromptTemplate).where(PromptTemplate.id == template_id))
            else:
                result = await db.execute(select(PromptTemplate).where(PromptTemplate.id == template_id))

            template = result.scalars().first()

            if not template:
                raise HTTPException(status_code=404, detail="Prompt template not found")

            return PromptTemplateModel(
                id=template.id,
                post_type=template.post_type,
                language=template.language,
                template_content=template.template_content,
                description=template.description,
                active=template.active
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching prompt template {template_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching prompt template: {str(e)}")

@router.post("/prompt-templates", response_model=PromptTemplateModel)
async def create_prompt_template(template_data: PromptTemplateModel):
    """Create a new prompt template"""
    async for db in get_async_db():
        # Check if template with same post_type and language already exists
        if is_sqlite_db():
            existing_result = db.execute(
                select(PromptTemplate).where(
                    PromptTemplate.post_type == template_data.post_type,
                    PromptTemplate.language == template_data.language
                )
            )
        else:
            existing_result = await db.execute(
                select(PromptTemplate).where(
                    PromptTemplate.post_type == template_data.post_type,
                    PromptTemplate.language == template_data.language
                )
            )

        existing_template = existing_result.scalars().first()

        if existing_template:
            raise HTTPException(
                status_code=400,
                detail=f"Prompt template for {template_data.post_type} in {template_data.language} already exists"
            )

        # Create new template
        template = PromptTemplate(
            post_type=template_data.post_type,
            language=template_data.language,
            template_content=template_data.template_content,
            description=template_data.description,
            active=template_data.active
        )

        db.add(template)

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return template_data

@router.put("/prompt-templates/{template_id}", response_model=PromptTemplateModel)
async def update_prompt_template(template_id: int, template_data: PromptTemplateModel):
    """Update a prompt template"""
    async for db in get_async_db():
        if is_sqlite_db():
            result = db.execute(select(PromptTemplate).where(PromptTemplate.id == template_id))
        else:
            result = await db.execute(select(PromptTemplate).where(PromptTemplate.id == template_id))

        template = result.scalars().first()

        if not template:
            raise HTTPException(status_code=404, detail="Prompt template not found")

        # Update template
        template.post_type = template_data.post_type
        template.language = template_data.language
        template.template_content = template_data.template_content
        template.description = template_data.description
        template.active = template_data.active
        template.updated_at = datetime.now(timezone.utc)

        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()

        return template_data

@router.delete("/prompt-templates/{template_id}")
async def delete_prompt_template(template_id: int):
    """Delete a prompt template"""
    async for db in get_async_db():
        if is_sqlite_db():
            result = db.execute(select(PromptTemplate).where(PromptTemplate.id == template_id))
        else:
            result = await db.execute(select(PromptTemplate).where(PromptTemplate.id == template_id))

        template = result.scalars().first()

        if not template:
            raise HTTPException(status_code=404, detail="Prompt template not found")

        if is_sqlite_db():
            db.delete(template)
            db.commit()
        else:
            await db.delete(template)
            await db.commit()

        return {"message": "Prompt template deleted successfully"}

@router.get("/prompt-templates/by-type/{post_type}")
async def get_prompt_templates_by_type(post_type: str, language: str = "fa"):
    """Get prompt templates for a specific post type and language"""
    async for db in get_async_db():
        if is_sqlite_db():
            result = db.execute(
                select(PromptTemplate).where(
                    PromptTemplate.post_type == post_type,
                    PromptTemplate.language == language,
                    PromptTemplate.active == True
                )
            )
        else:
            result = await db.execute(
                select(PromptTemplate).where(
                    PromptTemplate.post_type == post_type,
                    PromptTemplate.language == language,
                    PromptTemplate.active == True
                )
            )

        template = result.scalars().first()

        if not template:
            # Return a basic fallback template
            return {
                "id": None,
                "post_type": post_type,
                "language": language,
                "template_content": "Generate content for {post_type} in {language}",
                "description": f"Fallback template for {post_type}",
                "active": True
            }

        return {
            "id": template.id,
            "post_type": template.post_type,
            "language": template.language,
            "template_content": template.template_content,
            "description": template.description,
            "active": template.active
        }
