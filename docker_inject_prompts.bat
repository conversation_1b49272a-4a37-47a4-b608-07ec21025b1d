@echo off
REM Docker script to inject Afghanistan Farsi prompts into MignalyBot database
REM This script should be run from the host machine to execute the injection inside the Docker container

echo 🚀 Starting Afghanistan Farsi prompts injection into Docker container...

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker first.
    exit /b 1
)

REM Find the MignalyBot container
set CONTAINER_NAME=mignalybot
for /f %%i in ('docker ps -q -f name=%CONTAINER_NAME%') do set CONTAINER_ID=%%i

if "%CONTAINER_ID%"=="" (
    echo ❌ MignalyBot container not found. Please make sure the container is running.
    echo    Try: docker ps to see running containers
    exit /b 1
)

echo ✅ Found MignalyBot container: %CONTAINER_ID%

REM Copy the injection script to the container
echo 📋 Copying injection script to container...
docker cp inject_afghanistan_prompts.py %CONTAINER_ID%:/app/inject_afghanistan_prompts.py

REM Make the script executable
docker exec %CONTAINER_ID% chmod +x /app/inject_afghanistan_prompts.py

REM Execute the injection script inside the container
echo 🔧 Executing prompts injection inside container...
docker exec %CONTAINER_ID% python /app/inject_afghanistan_prompts.py

REM Clean up - remove the script from container
echo 🧹 Cleaning up...
docker exec %CONTAINER_ID% rm -f /app/inject_afghanistan_prompts.py

echo 🎉 Afghanistan Farsi prompts injection completed!
echo 📝 The fa-af language prompts should now be available in the database.
echo 🔄 You may need to restart the application or wait for the next prompt request to see the changes.

pause
