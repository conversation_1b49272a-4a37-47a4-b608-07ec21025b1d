"""
Binance-style PnL image generator for trading performance updates
Creates high-quality mobile screenshot-style images showing trade results
"""

import logging
from PIL import Image, ImageDraw, ImageFont
from datetime import datetime, timezone
import os
from typing import Dict, Any
import random

logger = logging.getLogger(__name__)

class BinancePnLGenerator:
    """Generate Binance-style PnL images for trading results"""

    def __init__(self):
        self.width = 1200
        self.height = 675

        # Detect Docker environment for enhanced compatibility
        self.docker_mode = os.environ.get('DOCKER_ENV', 'false').lower() == 'true' or os.path.exists('/.dockerenv')
        if self.docker_mode:
            logger.info("🐳 Docker environment detected - using enhanced compatibility mode")

        # Updated color scheme to match Binance design
        self.bg_start = "#1A1D29"    # Gradient start
        self.bg_end = "#2B2F42"      # Gradient end
        self.text_primary = "#FFFFFF"  # Primary text (white)
        self.text_secondary = "#B7BCC8"  # Secondary text (light gray)
        self.green = "#0ECB81"       # Binance green
        self.red = "#F6465D"         # Binance red
        self.yellow = "#F0B90B"      # Binance yellow
        self.binance_yellow = "#F0B90B"  # Binance brand yellow

        # Load fonts (fallback to default if not available)
        self.font_paths = {
            'regular': self._find_font(['arial.ttf', 'DejaVuSans.ttf', 'liberation-sans.ttf']),
            'bold': self._find_font(['arialbd.ttf', 'DejaVuSans-Bold.ttf', 'liberation-sans-bold.ttf']),
            'light': self._find_font(['arial.ttf', 'DejaVuSans.ttf', 'liberation-sans.ttf'])
        }
    
    def _find_font(self, font_names):
        """Find available font file"""
        system_fonts = [
            '/System/Library/Fonts/',  # macOS
            '/usr/share/fonts/',       # Linux
            'C:/Windows/Fonts/',       # Windows
        ]
        
        for font_name in font_names:
            for font_dir in system_fonts:
                font_path = os.path.join(font_dir, font_name)
                if os.path.exists(font_path):
                    return font_path
        
        return None  # Will use default font
    
    def _get_font(self, font_type: str, size: int):
        """Get font with proper fallback and size handling"""
        min_size = max(size, 20)  # Minimum 20px for any font

        # Try to load TrueType fonts first
        font_attempts = [
            # Try custom font path
            lambda: ImageFont.truetype(self.font_paths.get(font_type), min_size) if self.font_paths.get(font_type) else None,
            # Try common system fonts
            lambda: ImageFont.truetype("arial.ttf", min_size),
            lambda: ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", min_size),
            lambda: ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", min_size),
            lambda: ImageFont.truetype("/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf", min_size),
            lambda: ImageFont.truetype("/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf", min_size),
        ]

        # Try TrueType fonts
        for i, attempt in enumerate(font_attempts):
            try:
                font = attempt()
                if font:
                    logger.debug(f"Loaded TrueType font {font_type} at size {min_size} (attempt {i+1})")
                    return font
            except Exception:
                continue

        # If no TrueType fonts available, create a larger default font
        logger.warning(f"No TrueType fonts available for {font_type}, using enhanced default font")

        # For Docker/environments without proper fonts, we need to simulate larger text
        # by using the default font multiple times or other techniques
        try:
            default_font = ImageFont.load_default()
            # Store the requested size for later use in drawing
            default_font._requested_size = min_size
            return default_font
        except Exception as e:
            logger.error(f"Failed to load any font: {e}")
            return ImageFont.load_default()

    def _draw_text_enhanced(self, draw, position, text, fill, font):
        """Enhanced text drawing that handles default fonts better"""
        x, y = position

        # Check if this is a default font that needs enhancement
        if hasattr(font, '_requested_size'):
            requested_size = font._requested_size

            # For large requested sizes with default font, use multiple techniques
            if requested_size >= 100:
                # For very large text (like PnL %), draw multiple times with slight offsets for boldness
                offsets = [(0, 0), (1, 0), (0, 1), (1, 1)]
                for dx, dy in offsets:
                    draw.text((x + dx, y + dy), text, fill=fill, font=font)
            elif requested_size >= 40:
                # For medium text, draw twice for slight boldness
                draw.text((x, y), text, fill=fill, font=font)
                draw.text((x + 1, y), text, fill=fill, font=font)
            else:
                # For small text, draw normally
                draw.text((x, y), text, fill=fill, font=font)
        else:
            # Normal TrueType font, draw normally
            draw.text((x, y), text, fill=fill, font=font)

    def _is_forex_pair(self, symbol):
        """Check if symbol is a forex pair (excluding indices/commodities that don't use pips)"""
        # Normalize symbol by removing slashes and converting to uppercase
        normalized_symbol = symbol.upper().replace('/', '')

        forex_pairs = [
            # Major forex pairs
            'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
            # Cross pairs
            'EURJPY', 'GBPJPY', 'EURGBP', 'EURAUD', 'EURCHF', 'AUDCHF', 'GBPCHF',
            'CADCHF', 'NZDCHF', 'NZDJPY', 'AUDCAD', 'AUDNZD', 'CADJPY', 'CHFJPY',
            'EURNZD', 'EURCAD', 'GBPAUD', 'GBPCAD', 'GBPNZD', 'NZDCAD', 'XAUUSD','DJIUSD'
            # Note: DJIUSD and XAUUSD removed - they don't use pip calculations
        ]
        return any(pair == normalized_symbol for pair in forex_pairs)

    def _create_gradient_background(self):
        """Create background using the provided bg.png file"""
        try:
            # Try to load the custom background image
            bg_path = os.path.join(os.path.dirname(__file__), "bg.png")
            if os.path.exists(bg_path):
                logger.info(f"🖼️ Loading custom background from: {bg_path}")
                bg_img = Image.open(bg_path)

                # Resize to match our dimensions
                bg_img = bg_img.resize((self.width, self.height), Image.Resampling.LANCZOS)

                # Convert to RGB if needed
                if bg_img.mode != 'RGB':
                    bg_img = bg_img.convert('RGB')

                logger.info(f"✅ Custom background loaded and resized to {self.width}x{self.height}")
                return bg_img
            else:
                logger.warning(f"⚠️ Custom background not found at {bg_path}, using fallback")
        except Exception as e:
            logger.error(f"❌ Error loading custom background: {e}")

        # Fallback: Create simple gradient background
        img = Image.new('RGB', (self.width, self.height), self.bg_start)

        # Create gradient effect
        for y in range(self.height):
            # Calculate gradient ratio
            ratio = y / self.height

            # Interpolate between start and end colors
            start_rgb = tuple(int(self.bg_start[i:i+2], 16) for i in (1, 3, 5))
            end_rgb = tuple(int(self.bg_end[i:i+2], 16) for i in (1, 3, 5))

            current_rgb = tuple(
                int(start_rgb[i] + (end_rgb[i] - start_rgb[i]) * ratio)
                for i in range(3)
            )

            # Draw gradient line
            draw = ImageDraw.Draw(img)
            draw.line([(0, y), (self.width, y)], fill=current_rgb)

        return img



    def _draw_rounded_rectangle(self, draw, coords, radius, fill):
        """Draw a rounded rectangle"""
        x1, y1, x2, y2 = coords

        # Draw main rectangle
        draw.rectangle([x1 + radius, y1, x2 - radius, y2], fill=fill)
        draw.rectangle([x1, y1 + radius, x2, y2 - radius], fill=fill)

        # Draw corners
        draw.pieslice([x1, y1, x1 + 2*radius, y1 + 2*radius], 180, 270, fill=fill)
        draw.pieslice([x2 - 2*radius, y1, x2, y1 + 2*radius], 270, 360, fill=fill)
        draw.pieslice([x1, y2 - 2*radius, x1 + 2*radius, y2], 90, 180, fill=fill)
        draw.pieslice([x2 - 2*radius, y2 - 2*radius, x2, y2], 0, 90, fill=fill)
    
    def generate_pnl_image(self, trade_data: Dict[str, Any]) -> str:
        """
        Generate a Binance-style PnL image
        
        Args:
            trade_data: Dictionary containing trade information:
                - symbol: Trading pair (e.g., "BTCUSD")
                - direction: "LONG" or "SHORT"
                - entry_price: Entry price
                - exit_price: Exit price (optional for partial TP)
                - profit_loss_pct: Profit/loss percentage
                - profit_loss_usdt: Profit/loss in USDT (optional)
                - position_size: Position size
                - leverage: Leverage (optional)
                - date: Trade date
                - tp_level: TP level (1, 2, 3) or None for final
                - status: Trade status
                - pips: Pips profit/loss for forex symbols (optional)
        
        Returns:
            str: Path to generated image
        """
        try:
            logger.info(f"🖼️ Starting PnL image generation...")
            logger.info(f"📊 Raw trade data: {trade_data}")

            # Create gradient background with diamond patterns
            img = self._create_gradient_background()
            draw = ImageDraw.Draw(img)
            logger.info(f"✅ Created background image: {self.width}x{self.height}")

            # Extract trade data
            symbol = trade_data.get('symbol', 'UNKNOWN')
            direction = trade_data.get('direction', 'LONG').upper()
            entry_price = trade_data.get('entry_price', 0)
            exit_price = trade_data.get('exit_price', entry_price)
            profit_loss_pct = trade_data.get('profit_loss_pct', 0)
            leverage = trade_data.get('leverage', 1)
            pips = trade_data.get('pips', 0)  # Pips for forex symbols

            # Debug logging
            logger.info(f"🖼️ Generating PnL image: {symbol} {direction} | "
                       f"Entry: {entry_price} | Exit: {exit_price} | "
                       f"P&L: {profit_loss_pct}% | Leverage: {leverage}x")

            # Ensure we have valid numeric values
            entry_price = float(entry_price) if entry_price else 0.0
            exit_price = float(exit_price) if exit_price else entry_price
            profit_loss_pct = float(profit_loss_pct) if profit_loss_pct else 0.0
            leverage = int(leverage) if leverage else 1

            logger.info(f"📈 Processed values: Entry={entry_price}, Exit={exit_price}, P&L={profit_loss_pct}%, Leverage={leverage}x")

            # Determine colors based on profit/loss
            if profit_loss_pct > 0:
                pnl_color = self.green
            elif profit_loss_pct < 0:
                pnl_color = self.red
            else:
                pnl_color = self.yellow

            # Trading platform header
            y_offset = 60

            # Platform logo (generic trading symbol)
            logo_font = self._get_font('bold', 36)
            self._draw_text_enhanced(draw, (80, y_offset), "📈", self.binance_yellow, logo_font)

            brand_font = self._get_font('bold', 32)
            self._draw_text_enhanced(draw, (140, y_offset), "TRADING", self.binance_yellow, brand_font)

            futures_font = self._get_font('bold', 32)
            self._draw_text_enhanced(draw, (140, y_offset + 40), "RESULTS", self.text_primary, futures_font)

            y_offset += 120
            
            # Trading pair and direction info
            pair_info_y = y_offset

            # Direction, leverage, and pair on one line
            direction_font = self._get_font('regular', 36)
            direction_color = self.green if direction == "LONG" else self.red

            direction_text = direction
            self._draw_text_enhanced(draw, (80, pair_info_y), direction_text, direction_color, direction_font)

            # Separator
            separator_x = 80 + draw.textbbox((0, 0), direction_text, font=direction_font)[2] + 40
            self._draw_text_enhanced(draw, (separator_x, pair_info_y), "|", self.text_secondary, direction_font)

            # Leverage (only show if > 1)
            if leverage and leverage > 1:
                leverage_font = self._get_font('bold', 36)
                leverage_x = separator_x + 30
                self._draw_text_enhanced(draw, (leverage_x, pair_info_y), f"{leverage}x", self.text_primary, leverage_font)

                # Separator
                sep2_x = leverage_x + draw.textbbox((0, 0), f"{leverage}x", font=leverage_font)[2] + 25
                self._draw_text_enhanced(draw, (sep2_x, pair_info_y), "|", self.text_secondary, direction_font)

                # Trading pair
                pair_font = self._get_font('regular', 36)
                pair_x = sep2_x + 30
                # Clean symbol name
                clean_symbol = symbol.replace("/", "").replace("-", "")
                symbol_text = clean_symbol
                self._draw_text_enhanced(draw, (pair_x, pair_info_y), symbol_text, self.text_primary, pair_font)
            else:
                # Trading pair without leverage
                pair_font = self._get_font('regular', 36)
                pair_x = separator_x + 30
                # Clean symbol name
                clean_symbol = symbol.replace("/", "").replace("-", "")
                symbol_text = clean_symbol
                self._draw_text_enhanced(draw, (pair_x, pair_info_y), symbol_text, self.text_primary, pair_font)

            y_offset += 80

            # Main PnL display - large and prominent
            pnl_font = self._get_font('bold', 80)

            # Format PnL percentage as whole numbers (no decimals)
            pnl_text = f"{round(profit_loss_pct):+d}%"
            self._draw_text_enhanced(draw, (80, y_offset), pnl_text, pnl_color, pnl_font)

            # Add pips information for forex symbols ONLY
            if self._is_forex_pair(symbol):
                y_offset += 90
                pips_font = self._get_font('regular', 36)

                # Ensure pips is a valid number
                pips_value = float(pips) if pips is not None else 0.0

                # Determine pips sign based on profit/loss, not just pips value
                # If the trade is profitable (positive %), pips should be positive
                # If the trade is losing (negative %), pips should be negative
                if profit_loss_pct > 0:
                    pips_sign = "+" if pips_value > 0 else "+"
                    pips_display = abs(pips_value)
                elif profit_loss_pct < 0:
                    pips_sign = "-"
                    pips_display = abs(pips_value)
                else:
                    pips_sign = ""
                    pips_display = abs(pips_value)

                pips_text = f"{pips_sign}{pips_display:.1f} pips"
                self._draw_text_enhanced(draw, (80, y_offset), pips_text, pnl_color, pips_font)
                logger.info(f"📊 Added pips display: {pips_text} for forex symbol {symbol}")
                y_offset += 50
            else:
                y_offset += 120

            # Price details section - arranged horizontally
            content_x = 80
            content_y = y_offset

            details_font = self._get_font('regular', 32)
            label_font = self._get_font('light', 28)

            # Entry Price
            self._draw_text_enhanced(draw, (content_x, content_y), "Entry Price", self.text_secondary, label_font)
            # Smart price formatting - always show meaningful precision
            if entry_price >= 1000:
                entry_price_text = f"{entry_price:,.2f}"
            elif entry_price >= 100:
                entry_price_text = f"{entry_price:.3f}"
            elif entry_price >= 1:
                entry_price_text = f"{entry_price:.5f}"
            elif entry_price >= 0.01:
                entry_price_text = f"{entry_price:.6f}"
            else:
                entry_price_text = f"{entry_price:.8f}"
            self._draw_text_enhanced(draw, (content_x + 300, content_y), entry_price_text, self.binance_yellow, details_font)

            content_y += 60

            # Last Price (exit price or current price)
            current_price = exit_price if exit_price and exit_price != entry_price else entry_price
            self._draw_text_enhanced(draw, (content_x, content_y), "Last Price", self.text_secondary, label_font)
            # Smart price formatting - always show meaningful precision
            if current_price >= 1000:
                last_price_text = f"{current_price:,.2f}"
            elif current_price >= 100:
                last_price_text = f"{current_price:.3f}"
            elif current_price >= 1:
                last_price_text = f"{current_price:.5f}"
            elif current_price >= 0.01:
                last_price_text = f"{current_price:.6f}"
            else:
                last_price_text = f"{current_price:.8f}"
            self._draw_text_enhanced(draw, (content_x + 300, content_y), last_price_text, self.binance_yellow, details_font)

            # Additional trading info section (bottom right area)
            info_section_y = self.height - 120
            info_font = self._get_font('light', 24)

            # Position info in bottom right
            info_x = self.width - 300

            # Add timestamp
            timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M UTC")
            ##self._draw_text_enhanced(draw, (info_x, info_section_y), f"Generated: {timestamp}", self.text_secondary, info_font)

            # Footer
            footer_y = self.height - 40
            footer_font = self._get_font('light', 24)
            footer_text = "Generated by MignalyBot"
            footer_bbox = draw.textbbox((0, 0), footer_text, font=footer_font)
            footer_width = footer_bbox[2] - footer_bbox[0]
            self._draw_text_enhanced(draw, ((self.width - footer_width) // 2, footer_y), footer_text,
                                   self.text_secondary, footer_font)
            
            # Save image
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            # Clean symbol name for filename (remove special characters)
            clean_symbol = symbol.replace("/", "").replace("\\", "").replace(":", "").replace("*", "").replace("?", "").replace('"', "").replace("<", "").replace(">", "").replace("|", "")
            filename = f"pnl_{clean_symbol}_{timestamp}.png"

            # Ensure images directory exists
            os.makedirs("images/pnl", exist_ok=True)
            filepath = os.path.join("images/pnl", filename)
            
            # Verify image before saving
            if img.size != (self.width, self.height):
                logger.warning(f"Image size mismatch: expected {self.width}x{self.height}, got {img.size}")

            img.save(filepath, "PNG", quality=95)

            # Verify file was created and has reasonable size
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath)
                logger.info(f"✅ PnL image saved: {filepath} ({self.width}x{self.height}, {file_size} bytes)")

                # Sanity check - file should be at least 10KB for a proper image
                if file_size < 10000:
                    logger.warning(f"⚠️ Generated image seems too small: {file_size} bytes")

                return filepath
            else:
                logger.error(f"❌ Failed to save image file: {filepath}")
                return None
            
        except Exception as e:
            logger.error(f"Error generating PnL image: {e}", exc_info=True)
            return None
