"""
Main application service for MignalyBot v2

Coordinates all application components:
- Database management
- AI integration
- Data collection
- Telegram bot
- Admin interface
- Background tasks
"""

import asyncio
import logging
from typing import List, Optional
from contextlib import asynccontextmanager

from core.config.settings import Settings
from core.exceptions.base import <PERSON>gna<PERSON><PERSON><PERSON>Ex<PERSON>, handle_exception
from database.connection.manager import DatabaseManager, set_db_manager
from ai.clients.qwen import QwenClient
from ai.cache.manager import CacheManager
from ai.strategies.generator import StrategyGenerator


class ApplicationService:
    """Main application service coordinator"""
    
    def __init__(self, settings: Settings, db_manager: DatabaseManager):
        self.settings = settings
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        
        # Core components
        self.cache_manager: Optional[CacheManager] = None
        self.qwen_client: Optional[QwenClient] = None
        self.strategy_generator: Optional[StrategyGenerator] = None
        
        # Service components
        self.data_collection_service = None
        self.telegram_service = None
        self.admin_service = None
        self.content_service = None
        
        # Background tasks
        self.running_tasks: List[asyncio.Task] = []
    
    async def initialize(self):
        """Initialize all application components"""
        try:
            self.logger.info("Initializing application services")
            
            # Set global database manager
            set_db_manager(self.db_manager)
            
            # Initialize database tables
            await self._initialize_database()
            
            # Initialize AI components
            await self._initialize_ai_components()
            
            # Initialize other services
            await self._initialize_services()
            
            self.logger.info("Application services initialized successfully")
            
        except Exception as e:
            handle_exception(e, self.logger, "application_initialization", reraise=True)
    
    async def _initialize_database(self):
        """Initialize database tables and default data"""
        try:
            # Create tables
            from database.models.base import Base
            
            if self.settings.database.is_sqlite:
                # For SQLite, use sync operations
                Base.metadata.create_all(bind=self.db_manager.sync_engine)
            else:
                # For async databases
                async with self.db_manager.async_engine.begin() as conn:
                    await conn.run_sync(Base.metadata.create_all)
            
            # Initialize default configuration
            await self._initialize_default_config()
            
            self.logger.info("Database initialized successfully")
            
        except Exception as e:
            raise MignalyBotException(
                "Failed to initialize database",
                context={'database_url': self.settings.database.url},
                original_exception=e
            )
    
    async def _initialize_default_config(self):
        """Initialize default configuration if not exists"""
        try:
            from database.models.core import Config
            
            async with self.db_manager.get_async_session() as session:
                # Check if config exists
                if self.db_manager.settings.database.is_sqlite:
                    from sqlalchemy import select
                    result = session.execute(select(Config))
                else:
                    from sqlalchemy import select
                    result = await session.execute(select(Config))
                
                config = result.scalars().first()
                
                if not config:
                    self.logger.info("Creating default configuration")
                    default_config = Config(
                        qwen_api_key=self.settings.ai.qwen_api_key,
                        qwen_endpoint=self.settings.ai.qwen_endpoint,
                        qwen_model=self.settings.ai.qwen_model,
                        default_language=self.settings.default_language,
                        timezone=self.settings.timezone,
                        symbols=",".join(self.settings.data_collection.symbols),
                        timeframes=",".join(self.settings.data_collection.timeframes),
                        enable_news=self.settings.data_collection.enable_news,
                        enable_signals=self.settings.data_collection.enable_signals,
                        enable_calendar=self.settings.data_collection.enable_calendar,
                        enable_ai_strategies=True,
                        max_tokens_per_request=self.settings.ai.max_tokens,
                        ai_cache_ttl=self.settings.ai.cache_ttl,
                        batch_size=self.settings.data_collection.batch_size
                    )
                    session.add(default_config)
                    
                    if self.db_manager.settings.database.is_sqlite:
                        session.commit()
                    else:
                        await session.commit()
                    
                    self.logger.info("Default configuration created")
        
        except Exception as e:
            self.logger.error(f"Error initializing default config: {e}")
    
    async def _initialize_ai_components(self):
        """Initialize AI-related components"""
        try:
            # Initialize cache manager
            self.cache_manager = CacheManager(
                db_manager=self.db_manager,
                max_memory_items=1000
            )
            
            # Initialize Qwen client
            self.qwen_client = QwenClient(
                settings=self.settings.ai,
                cache_manager=self.cache_manager
            )
            
            # Initialize strategy generator
            self.strategy_generator = StrategyGenerator(self.qwen_client)
            
            # Test AI connection
            await self._test_ai_connection()
            
            self.logger.info("AI components initialized successfully")
            
        except Exception as e:
            raise MignalyBotException(
                "Failed to initialize AI components",
                context={'qwen_endpoint': self.settings.ai.qwen_endpoint},
                original_exception=e
            )
    
    async def _test_ai_connection(self):
        """Test AI connection"""
        if not self.settings.ai.qwen_api_key:
            self.logger.warning("Qwen API key not configured, skipping AI connection test")
            return
        
        try:
            health_check = await self.qwen_client.health_check()
            if health_check['status'] != 'healthy':
                raise MignalyBotException(
                    f"AI health check failed: {health_check.get('error', 'Unknown error')}"
                )
            
            self.logger.info("AI connection test successful")
            
        except Exception as e:
            self.logger.error(f"AI connection test failed: {e}")
            # Don't raise exception here, allow app to start without AI
    
    async def _initialize_services(self):
        """Initialize other application services"""
        try:
            # Initialize data collection service
            from data.service import DataCollectionService
            self.data_collection_service = DataCollectionService(self.settings, self.db_manager)

            # Initialize Telegram service
            from telegram_bot.service import TelegramService
            self.telegram_service = TelegramService(self.settings, self.db_manager)

            # Initialize content service
            from services.content import ContentService
            self.content_service = ContentService(self.settings, self.db_manager, self.qwen_client)

            # Initialize admin service
            from admin.service import AdminService
            self.admin_service = AdminService(
                self.settings,
                self.db_manager,
                self.qwen_client,
                self.data_collection_service,
                self.telegram_service,
                self.content_service
            )

            self.logger.info("Application services initialized")

        except Exception as e:
            raise MignalyBotException(
                "Failed to initialize application services",
                original_exception=e
            )
    
    async def start_all_services(self) -> List[asyncio.Task]:
        """Start all application services and return their tasks"""
        tasks = []
        
        try:
            # Start data collection service
            if self.data_collection_service:
                task = asyncio.create_task(self.data_collection_service.start())
                tasks.append(task)
                self.logger.info("Data collection service started")
            
            # Start Telegram service
            if self.telegram_service:
                task = asyncio.create_task(self.telegram_service.start())
                tasks.append(task)
                self.logger.info("Telegram service started")
            
            # Start admin service
            if self.admin_service:
                task = asyncio.create_task(self.admin_service.start())
                tasks.append(task)
                self.logger.info("Admin service started")
            
            # Start content service
            if self.content_service:
                task = asyncio.create_task(self.content_service.start())
                tasks.append(task)
                self.logger.info("Content service started")
            
            # Start background maintenance tasks
            maintenance_task = asyncio.create_task(self._run_maintenance_tasks())
            tasks.append(maintenance_task)
            
            self.running_tasks.extend(tasks)
            self.logger.info(f"Started {len(tasks)} application services")
            
            return tasks
            
        except Exception as e:
            # Cancel any started tasks
            for task in tasks:
                task.cancel()
            
            handle_exception(e, self.logger, "start_all_services", reraise=True)
    
    async def _run_maintenance_tasks(self):
        """Run background maintenance tasks"""
        try:
            while True:
                await asyncio.sleep(300)  # Run every 5 minutes
                
                try:
                    # Perform health checks
                    await self._perform_health_checks()
                    
                    # Clean up expired cache entries
                    if self.cache_manager:
                        await self.cache_manager._cleanup_expired()
                    
                    # Log performance statistics
                    await self._log_performance_stats()
                    
                except Exception as e:
                    self.logger.error(f"Maintenance task error: {e}")
        
        except asyncio.CancelledError:
            self.logger.info("Maintenance tasks cancelled")
    
    async def _perform_health_checks(self):
        """Perform health checks on all components"""
        try:
            # Database health check
            db_health = await self.db_manager.health_check()
            if db_health['status'] != 'healthy':
                self.logger.warning(f"Database health check failed: {db_health}")
            
            # AI health check
            if self.qwen_client:
                ai_health = await self.qwen_client.health_check()
                if ai_health['status'] != 'healthy':
                    self.logger.warning(f"AI health check failed: {ai_health}")
            
        except Exception as e:
            self.logger.error(f"Health check error: {e}")
    
    async def _log_performance_stats(self):
        """Log performance statistics"""
        try:
            # Database stats
            db_stats = await self.db_manager.get_connection_stats()
            self.logger.info(f"Database stats: {db_stats}")
            
            # AI stats
            if self.qwen_client:
                ai_stats = await self.qwen_client.get_stats()
                self.logger.info(f"AI stats: {ai_stats}")
            
            # Cache stats
            if self.cache_manager:
                cache_stats = await self.cache_manager.get_stats()
                self.logger.info(f"Cache stats: {cache_stats}")
            
        except Exception as e:
            self.logger.error(f"Performance stats error: {e}")
    
    async def shutdown(self):
        """Gracefully shutdown all services"""
        try:
            self.logger.info("Shutting down application services")
            
            # Cancel all running tasks
            for task in self.running_tasks:
                if not task.done():
                    task.cancel()
            
            # Wait for tasks to complete
            if self.running_tasks:
                await asyncio.gather(*self.running_tasks, return_exceptions=True)
            
            # Shutdown individual services
            if self.data_collection_service:
                await self.data_collection_service.shutdown()
            
            if self.telegram_service:
                await self.telegram_service.shutdown()
            
            if self.admin_service:
                await self.admin_service.shutdown()
            
            if self.content_service:
                await self.content_service.shutdown()
            
            # Close AI components
            if self.qwen_client:
                await self.qwen_client.close()
            
            if self.cache_manager:
                await self.cache_manager.close()
            
            self.logger.info("Application services shutdown completed")
            
        except Exception as e:
            self.logger.error(f"Error during application shutdown: {e}")
    
    @asynccontextmanager
    async def lifespan(self):
        """Application lifespan context manager"""
        try:
            await self.initialize()
            yield self
        finally:
            await self.shutdown()
