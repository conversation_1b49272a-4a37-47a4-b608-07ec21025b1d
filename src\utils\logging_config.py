"""
Logging configuration for MignalyBot
"""

import os
import logging
import logging.handlers
from datetime import datetime, timezone

def setup_logging(log_level=None, log_file=None):
    """
    Set up logging configuration
    
    Args:
        log_level (str, optional): Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file (str, optional): Path to log file
    """
    # Get log level from environment variable if not provided
    if log_level is None:
        log_level = os.getenv("LOG_LEVEL", "INFO")
    
    # Convert log level string to logging level
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        numeric_level = logging.INFO
    
    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)
    
    # Generate log file name if not provided
    if log_file is None:
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d")
        log_file = f"logs/mignalybot_{timestamp}.log"
    
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(numeric_level)

    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Set specific loggers to higher levels to reduce noise
    if numeric_level > logging.DEBUG:
        # Suppress DEBUG messages from third-party libraries
        logging.getLogger("httpcore").setLevel(logging.INFO)
        logging.getLogger("httpx").setLevel(logging.INFO)
        logging.getLogger("urllib3").setLevel(logging.INFO)
        logging.getLogger("requests").setLevel(logging.INFO)
        logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
        logging.getLogger("sqlalchemy.pool").setLevel(logging.WARNING)
        logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(numeric_level)
    console_format = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    console_handler.setFormatter(console_format)
    logger.addHandler(console_handler)

    # Force console handler to DEBUG level if DEBUG is requested
    if numeric_level <= logging.DEBUG:
        console_handler.setLevel(logging.DEBUG)
    
    # Create file handler
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=10 * 1024 * 1024,  # 10 MB
        backupCount=5
    )
    file_handler.setLevel(numeric_level)
    file_format = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    file_handler.setFormatter(file_format)
    logger.addHandler(file_handler)
    
    # Set specific log levels for some modules
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("matplotlib").setLevel(logging.WARNING)
    logging.getLogger("PIL").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("telegram").setLevel(logging.WARNING)

    # Enable debug logging for our application modules
    if numeric_level <= logging.DEBUG:
        logging.getLogger("src").setLevel(logging.DEBUG)
        logging.getLogger("data_collection").setLevel(logging.DEBUG)
        logging.getLogger("ai_integration").setLevel(logging.DEBUG)
        logging.getLogger("admin").setLevel(logging.DEBUG)
        logging.getLogger("strategies").setLevel(logging.DEBUG)
        logging.getLogger("telegram").setLevel(logging.DEBUG)
        # Enable SQLAlchemy logging in debug mode
        logging.getLogger("sqlalchemy.engine").setLevel(logging.INFO)
        logging.getLogger("sqlalchemy.pool").setLevel(logging.INFO)

    # Log the configuration
    logger.info(f"Logging configured with level {log_level}")
    logger.info(f"Log file: {log_file}")
    logger.info(f"Debug mode: {numeric_level <= logging.DEBUG}")

    return logger
