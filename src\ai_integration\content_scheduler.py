"""
Greeting post scheduler for <PERSON><PERSON>lyBot
Automatically generates greeting posts at 9 AM Tehran time daily
"""

import asyncio
import logging
from datetime import datetime, time, timedelta
import pytz
from sqlalchemy import select

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import Config, Channel
from src.ai_integration.content_generator import generate_greeting_posts
from src.ai_integration.qwen_client import QwenClient
from src.utils.helpers import get_timezone, get_current_time

logger = logging.getLogger(__name__)

async def greeting_generation_scheduler():
    """
    Scheduler that runs greeting post generation automatically at 9 AM Tehran time daily
    """
    logger.info("Starting greeting post scheduler")

    while True:
        try:
            # Get current time in configured timezone
            now_local = get_current_time()
            target_time = time(9, 0)  # 9:00 AM

            # Calculate next 11 AM in local timezone
            next_run = now_local.replace(
                hour=target_time.hour,
                minute=target_time.minute,
                second=0,
                microsecond=0
            )

            # If we've already passed 9 AM today, schedule for tomorrow
            if now_local.time() >= target_time:
                next_run += timedelta(days=1)

            # Calculate seconds until next run
            time_until_run = (next_run - now_local).total_seconds()

            logger.info(f"Next greeting post generation scheduled for: {next_run.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            logger.info(f"Time until next run: {time_until_run / 3600:.2f} hours")

            # Wait until the scheduled time
            await asyncio.sleep(time_until_run)

            # Run greeting post generation only
            logger.info(f"Running scheduled greeting post generation at 9 AM {get_timezone().zone} time")
            posts_created = await generate_greeting_posts_for_all_channels()
            logger.info(f"Scheduled greeting post generation completed. Created {posts_created} greeting posts.")

        except Exception as e:
            logger.error(f"Error in greeting post scheduler: {e}", exc_info=True)
            # Wait 1 hour before retrying on error
            await asyncio.sleep(3600)

async def generate_greeting_posts_for_all_channels():
    """
    Generate greeting posts for all channels that have greeting posts enabled
    """
    posts_created = 0

    async for db in get_async_db():
        try:
            # Get all active channels
            if is_sqlite_db():
                channels_result = db.execute(select(Channel).where(Channel.active == True))
            else:
                channels_result = await db.execute(select(Channel).where(Channel.active == True))

            channels = channels_result.scalars().all()

            # Filter channels that have greeting posts enabled
            greeting_channels = []
            for channel in channels:
                post_types = set(channel.post_types.split(",")) if channel.post_types else set()
                if "greeting" in post_types:
                    greeting_channels.append(channel)
                else:
                    logger.info(f"⏭️ Greeting posts disabled for channel {channel.name}")

            if not greeting_channels:
                logger.info("No channels have greeting posts enabled")
                return 0

            # Generate ONE sticker for ALL channels (if any channel has stickers enabled)
            sticker_path = None
            sticker_enabled_channels = [ch for ch in greeting_channels if getattr(ch, 'enable_date_stickers', True)]

            if sticker_enabled_channels:
                logger.info(f"🎨 Generating ONE date sticker for {len(sticker_enabled_channels)} channels...")
                from src.image_generation.date_sticker_generator import DateStickerGenerator
                sticker_generator = DateStickerGenerator()
                sticker_path = sticker_generator.generate_date_sticker()

                if sticker_path:
                    logger.info(f"✅ Global date sticker generated: {sticker_path}")
                else:
                    logger.warning(f"⚠️ Failed to generate global date sticker")

            # Initialize Qwen client
            qwen_client = QwenClient()

            for channel in greeting_channels:
                try:
                    logger.info(f"👋 Generating greeting post for channel {channel.name}")
                    greeting_count = await generate_greeting_posts(db, channel, qwen_client, global_sticker_path=sticker_path)
                    posts_created += greeting_count
                    logger.info(f"✅ Created {greeting_count} greeting posts for channel {channel.name}")

                except Exception as e:
                    logger.error(f"Error generating greeting post for channel {channel.name}: {e}", exc_info=True)
                    continue

        except Exception as e:
            logger.error(f"Error getting channels for greeting generation: {e}", exc_info=True)

    return posts_created

async def start_greeting_scheduler():
    """
    Start the greeting post scheduler
    """
    logger.info("Starting greeting post scheduler")

    # Check if greeting generation is enabled
    async for db in get_async_db():
        try:
            if is_sqlite_db():
                config_result = db.execute(select(Config))
            else:
                config_result = await db.execute(select(Config))

            config = config_result.scalars().first()

            if not config:
                logger.warning("No configuration found, greeting scheduler will not start")
                return

            # Always run the greeting scheduler
            logger.info("Configuration found, starting greeting scheduler")

        except Exception as e:
            logger.error(f"Error checking configuration: {e}")
            return

    # Start the scheduler
    await greeting_generation_scheduler()

def get_next_greeting_generation_time():
    """
    Get the next scheduled greeting generation time

    Returns:
        datetime: Next greeting generation time in configured timezone
    """
    now_local = get_current_time()
    target_time = time(9, 0)  # 9:00 AM

    # Calculate next 11 AM in local timezone
    next_run = now_local.replace(
        hour=target_time.hour,
        minute=target_time.minute,
        second=0,
        microsecond=0
    )

    # If we've already passed 9 AM today, schedule for tomorrow
    if now_local.time() >= target_time:
        next_run += timedelta(days=1)

    return next_run

def get_time_until_next_generation():
    """
    Get the time remaining until next greeting generation

    Returns:
        timedelta: Time remaining until next generation
    """
    now_local = get_current_time()
    next_run = get_next_greeting_generation_time()
    return next_run - now_local
