#!/usr/bin/env python3
"""
Complete deployment script for Mi<PERSON>lyBot
This script handles all deployment tasks including:
1. Database setup and migrations
2. Prompt system initialization
3. Default data population
4. Environment validation
5. System health checks
"""

import asyncio
import logging
import sys
import os
import subprocess
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('deployment.log')
    ]
)
logger = logging.getLogger(__name__)

async def check_environment():
    """Check if all required environment variables are set"""
    logger.info("🔍 Checking environment variables...")
    
    required_vars = [
        'DATABASE_URL',
        'TELEGRAM_BOT_TOKEN',
        'QWEN_API_KEY',
        'QWEN_API_URL',
        'TIMEZONE'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        return False
    
    logger.info("✅ All required environment variables are set")
    return True

async def setup_database():
    """Initialize database and run migrations"""
    logger.info("🗄️ Setting up database...")
    
    try:
        from src.database.setup import init_db
        await init_db()
        logger.info("✅ Database initialized successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Database setup failed: {e}")
        return False

async def run_prompt_migration():
    """Run the prompt system migration"""
    logger.info("📝 Setting up prompt management system...")
    
    try:
        # Import and run the prompt setup script
        from scripts.setup_prompt_system import main as setup_prompts
        await setup_prompts()
        logger.info("✅ Prompt system setup completed")
        return True
    except Exception as e:
        logger.error(f"❌ Prompt system setup failed: {e}")
        return False

async def create_default_admin():
    """Create default admin user if not exists"""
    logger.info("👤 Setting up default admin user...")
    
    try:
        from src.database.setup import get_async_db, is_sqlite_db
        from src.database.models import User
        from sqlalchemy import select, text
        from werkzeug.security import generate_password_hash
        
        async for db in get_async_db():
            # Check if admin user exists
            if is_sqlite_db():
                result = db.execute(select(User).where(User.username == 'admin'))
                admin_user = result.scalars().first()
            else:
                result = await db.execute(select(User).where(User.username == 'admin'))
                admin_user = result.scalars().first()
            
            if not admin_user:
                # Create admin user
                admin_user = User(
                    username='admin',
                    password_hash=generate_password_hash('admin123'),
                    is_admin=True
                )
                
                if is_sqlite_db():
                    db.add(admin_user)
                    db.commit()
                else:
                    db.add(admin_user)
                    await db.commit()
                
                logger.info("✅ Default admin user created (username: admin, password: admin123)")
            else:
                logger.info("✅ Admin user already exists")
            
            break
        
        return True
    except Exception as e:
        logger.error(f"❌ Admin user setup failed: {e}")
        return False

async def setup_default_strategies():
    """Setup default trading strategies"""
    logger.info("📊 Setting up default trading strategies...")
    
    try:
        from src.strategies.default_strategies import create_default_strategies
        await create_default_strategies()
        logger.info("✅ Default trading strategies created")
        return True
    except Exception as e:
        logger.error(f"❌ Default strategies setup failed: {e}")
        return False

async def verify_api_connections():
    """Verify external API connections"""
    logger.info("🌐 Verifying API connections...")
    
    try:
        # Test MT5 API
        import aiohttp
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get('http://154.53.166.53:8000/api/market/job-status', timeout=10) as response:
                    if response.status == 200:
                        logger.info("✅ MT5 API connection successful")
                    else:
                        logger.warning(f"⚠️ MT5 API returned status {response.status}")
            except Exception as e:
                logger.warning(f"⚠️ MT5 API connection failed: {e}")
        
        # Test Forex Factory API
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://nfs.faireconomy.media/ff_calendar_thisweek.json', timeout=10) as response:
                    if response.status == 200:
                        logger.info("✅ Forex Factory API connection successful")
                    else:
                        logger.warning(f"⚠️ Forex Factory API returned status {response.status}")
        except Exception as e:
            logger.warning(f"⚠️ Forex Factory API connection failed: {e}")
        
        # Test Qwen API
        try:
            from src.ai_integration.qwen_client import QwenClient
            client = QwenClient()
            # Simple test to verify API key and connection
            test_response = await client.generate_content("Test connection", max_tokens=10)
            if test_response:
                logger.info("✅ Qwen API connection successful")
            else:
                logger.warning("⚠️ Qwen API test failed")
        except Exception as e:
            logger.warning(f"⚠️ Qwen API connection failed: {e}")
        
        return True
    except Exception as e:
        logger.error(f"❌ API verification failed: {e}")
        return False

async def run_system_health_check():
    """Run comprehensive system health check"""
    logger.info("🏥 Running system health check...")
    
    try:
        # Check database connectivity
        from src.database.setup import get_async_db
        async for db in get_async_db():
            logger.info("✅ Database connectivity check passed")
            break
        
        # Check prompt templates
        from src.ai_integration.qwen_client import QwenClient
        client = QwenClient()
        
        test_template = await client.get_prompt_template("signals", "fa")
        if test_template:
            logger.info("✅ Prompt template system check passed")
        else:
            logger.warning("⚠️ Prompt template system check failed")
        
        # Check configuration
        from src.database.models import Config
        from sqlalchemy import select
        
        async for db in get_async_db():
            if hasattr(db, 'execute'):
                result = db.execute(select(Config))
            else:
                result = await db.execute(select(Config))
            
            config = result.scalars().first()
            if config:
                logger.info("✅ Configuration check passed")
            else:
                logger.warning("⚠️ No configuration found")
            break
        
        logger.info("✅ System health check completed")
        return True
    except Exception as e:
        logger.error(f"❌ System health check failed: {e}")
        return False

async def main():
    """Main deployment function"""
    logger.info("🚀 Starting MignalyBot Deployment")
    logger.info("=" * 60)
    
    success_count = 0
    total_steps = 7
    
    # Step 1: Check environment
    if await check_environment():
        success_count += 1
    
    # Step 2: Setup database
    if await setup_database():
        success_count += 1
    
    # Step 3: Run prompt migration
    if await run_prompt_migration():
        success_count += 1
    
    # Step 4: Create default admin
    if await create_default_admin():
        success_count += 1
    
    # Step 5: Setup default strategies
    if await setup_default_strategies():
        success_count += 1
    
    # Step 6: Verify API connections
    if await verify_api_connections():
        success_count += 1
    
    # Step 7: Run health check
    if await run_system_health_check():
        success_count += 1
    
    logger.info("=" * 60)
    logger.info(f"🎯 Deployment completed: {success_count}/{total_steps} steps successful")
    
    if success_count == total_steps:
        logger.info("🎉 MignalyBot deployment completed successfully!")
        logger.info("")
        logger.info("📋 Next steps:")
        logger.info("1. Start the application: python main.py")
        logger.info("2. Access admin interface: http://localhost:8000")
        logger.info("3. Login with: admin / admin123")
        logger.info("4. Configure your channels and settings")
        logger.info("5. Test content generation")
        return True
    else:
        logger.error("💥 Deployment completed with errors. Please check the logs above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
