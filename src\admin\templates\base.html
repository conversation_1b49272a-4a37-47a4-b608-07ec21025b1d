<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}MignalyBot Admin{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --success-color: #2ecc71;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #1abc9c;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .sidebar {
            background-color: var(--secondary-color);
            color: white;
            min-height: 100vh;
            padding-top: 20px;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 10px 20px;
            margin-bottom: 5px;
            border-radius: 5px;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .content {
            padding: 20px;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            font-weight: bold;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }

        .navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color);
        }

        .stat-card {
            border-left: 4px solid var(--primary-color);
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .stat-card.primary {
            border-left-color: var(--primary-color);
        }

        .stat-card.success {
            border-left-color: var(--success-color);
        }

        .stat-card.warning {
            border-left-color: var(--warning-color);
        }

        .stat-card.danger {
            border-left-color: var(--danger-color);
        }

        .stat-card .stat-value {
            font-size: 24px;
            font-weight: bold;
        }

        .stat-card .stat-label {
            color: #6c757d;
            font-size: 14px;
        }

        .stat-card .stat-icon {
            font-size: 30px;
            opacity: 0.3;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar d-none d-md-block">
                <div class="text-center mb-4">
                    <h4>MignalyBot</h4>
                    <p class="text-muted">Admin Dashboard</p>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.url.path == '/' %}active{% endif %}" href="/">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.url.path == '/config' %}active{% endif %}" href="/config">
                            <i class="fas fa-cogs"></i> Configuration
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.url.path == '/channels' %}active{% endif %}" href="/channels">
                            <i class="fas fa-broadcast-tower"></i> Channels
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.url.path == '/strategies' %}active{% endif %}" href="/strategies">
                            <i class="fas fa-chart-line"></i> Strategies
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.url.path == '/prompts' %}active{% endif %}" href="/prompts">
                            <i class="fas fa-edit"></i> Prompt Templates
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.url.path == '/posts' %}active{% endif %}" href="/posts">
                            <i class="fas fa-file-alt"></i> Posts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.url.path == '/signals' %}active{% endif %}" href="/signals">
                            <i class="fas fa-signal"></i> Signals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.url.path == '/news' %}active{% endif %}" href="/news">
                            <i class="fas fa-newspaper"></i> News
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.url.path == '/events' %}active{% endif %}" href="/events">
                            <i class="fas fa-calendar-alt"></i> Events
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 ms-auto content">
                <!-- Navbar -->
                <nav class="navbar navbar-expand-lg navbar-light mb-4">
                    <div class="container-fluid">
                        <button class="navbar-toggler d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        <span class="navbar-brand">{% block page_title %}Dashboard{% endblock %}</span>
                        <div class="ms-auto d-flex align-items-center">
                            <div class="me-4 text-center">
                                <div id="realTimeClock" class="fw-bold fs-5"></div>
                                <div class="small text-muted">Current Time</div>
                            </div>
                            <span class="text-muted me-3">Welcome, {{ username }}</span>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-primary" id="refreshDataBtn">
                                    <i class="fas fa-sync-alt"></i> Refresh Data
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" id="generateContentBtn">
                                    <i class="fas fa-magic"></i> Generate Content
                                </button>
                            </div>
                        </div>
                    </div>
                </nav>

                <!-- Mobile Sidebar -->
                <div class="collapse d-md-none mb-3" id="sidebarMenu">
                    <div class="bg-light p-3 rounded">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link {% if request.url.path == '/' %}active{% endif %}" href="/">
                                    <i class="fas fa-tachometer-alt"></i> Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.url.path == '/config' %}active{% endif %}" href="/config">
                                    <i class="fas fa-cogs"></i> Configuration
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.url.path == '/channels' %}active{% endif %}" href="/channels">
                                    <i class="fas fa-broadcast-tower"></i> Channels
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.url.path == '/strategies' %}active{% endif %}" href="/strategies">
                                    <i class="fas fa-chart-line"></i> Strategies
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.url.path == '/prompts' %}active{% endif %}" href="/prompts">
                                    <i class="fas fa-edit"></i> Prompt Templates
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.url.path == '/posts' %}active{% endif %}" href="/posts">
                                    <i class="fas fa-file-alt"></i> Posts
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.url.path == '/signals' %}active{% endif %}" href="/signals">
                                    <i class="fas fa-signal"></i> Signals
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.url.path == '/news' %}active{% endif %}" href="/news">
                                    <i class="fas fa-newspaper"></i> News
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.url.path == '/events' %}active{% endif %}" href="/events">
                                    <i class="fas fa-calendar-alt"></i> Events
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Page Content -->
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Common JS -->
    <script>
        $(document).ready(function() {
            // Initialize real-time clock
            updateClock();
            // Update clock every second
            setInterval(updateClock, 1000);

            // Refresh data button
            $('#refreshDataBtn').click(function() {
                $.ajax({
                    url: '/api/actions/collect-data',
                    type: 'POST',
                    success: function(response) {
                        alert('Data collection started successfully!');
                    },
                    error: function(xhr) {
                        alert('Error: ' + xhr.responseJSON.detail);
                    }
                });
            });

            // Generate content button
            $('#generateContentBtn').click(function() {
                $.ajax({
                    url: '/api/actions/generate-content',
                    type: 'POST',
                    success: function(response) {
                        alert('Content generation started successfully!');
                    },
                    error: function(xhr) {
                        alert('Error: ' + xhr.responseJSON.detail);
                    }
                });
            });
        });

        // Server timezone info
        let serverTimezone = 'UTC';
        let serverTimeOffset = 0; // Difference between server time and browser time in milliseconds

        // Get server timezone info on page load
        $.ajax({
            url: '/api/system/timezone',
            type: 'GET',
            success: function(response) {
                serverTimezone = response.timezone_name || response.timezone;

                // Parse server time correctly
                const serverTimeStr = response.current_time + ' ' + response.utc_offset;
                const serverTime = new Date(serverTimeStr);
                const browserTime = new Date();
                serverTimeOffset = serverTime.getTime() - browserTime.getTime();

                // Log timezone info for debugging
                console.log('Server timezone:', serverTimezone);
                console.log('Server time:', response.current_time);
                console.log('Server UTC offset:', response.utc_offset);
                console.log('Server time offset (ms):', serverTimeOffset);

                // Update timezone display with proper format
                const timezoneDisplay = response.timezone_display || serverTimezone;
                $('.small.text-muted').text('Time (' + timezoneDisplay + ')');

                // Update clock immediately
                updateClock();
            },
            error: function(xhr) {
                console.error('Error getting timezone info:', xhr);
                // Fallback to browser time
                serverTimezone = 'Local Time';
                serverTimeOffset = 0;
                $('.small.text-muted').text('Time (' + serverTimezone + ')');
                updateClock();
            }
        });

        // Function to update the real-time clock
        function updateClock() {
            // Get current time and adjust by server timezone offset
            const now = new Date();
            const serverTime = new Date(now.getTime() + serverTimeOffset);

            // Format the time
            const timeString = serverTime.toLocaleTimeString('en-GB', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            $('#realTimeClock').text(timeString);
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
