"""
Strategy processor module for MignalyBot
Uses AI-based prompt strategies instead of code-based strategies
"""

import logging
import importlib.util
import sys
from datetime import datetime, timedelta, timezone
import pandas as pd
from sqlalchemy import select

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import Strategy, CandleData, TradingSignal, SignalStatus, TakeProfitHit
from src.strategies.ai_signal_generator import ai_signal_generator

logger = logging.getLogger(__name__)

async def process_strategies():
    """Process all active trading strategies"""
    start_time = datetime.now(timezone.utc)
    logger.info(f"🎯 Starting trading strategy processing at {start_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")

    try:
        # Get all active strategies
        strategies = []
        strategies_start = datetime.now(timezone.utc)
        logger.info("📋 Loading active strategies from database...")

        async for db in get_async_db():
            try:
                # Get all active strategies
                if is_sqlite_db():
                    result = db.execute(
                        select(Strategy).where(Strategy.active == True)
                    )
                else:
                    result = await db.execute(
                        select(Strategy).where(Strategy.active == True)
                    )

                strategies = result.scalars().all()
                break  # Exit the loop if successful
            except Exception as e:
                logger.error(f"Error getting active strategies: {e}", exc_info=True)
                # Continue to next iteration to try again with a new connection

        strategies_duration = (datetime.now(timezone.utc) - strategies_start).total_seconds()

        if not strategies:
            logger.warning(f"⚠️ No active strategies found (query took {strategies_duration:.2f}s)")
            return

        logger.info(f"📊 Found {len(strategies)} active strategies (query took {strategies_duration:.2f}s)")

        # Log strategy details
        for strategy in strategies:
            strategy_type = "AI-based" if strategy.strategy_prompt else "Code-based" if strategy.code else "Invalid"
            logger.info(f"📋 Strategy: {strategy.name} (ID: {strategy.id}) - Type: {strategy_type}")
            logger.info(f"   📈 Symbols: {strategy.symbols}")
            logger.info(f"   ⏰ Timeframes: {strategy.timeframes}")
            if strategy.strategy_prompt:
                logger.info(f"   🤖 Prompt length: {len(strategy.strategy_prompt)} characters")

        # Process each strategy
        for i, strategy in enumerate(strategies, 1):
            logger.info(f"🔄 Processing strategy {i}/{len(strategies)}: {strategy.name}")
            try:
                await process_strategy(strategy)
                logger.info(f"✅ Completed strategy {i}/{len(strategies)}: {strategy.name}")
            except Exception as e:
                logger.error(f"❌ Error processing strategy {strategy.name}: {e}", exc_info=True)
                # Continue with next strategy instead of failing the whole process

        # Update signal statuses
        logger.info("🔄 Updating signal statuses...")
        try:
            await update_signal_statuses()
            logger.info("✅ Signal statuses updated")
        except Exception as e:
            logger.error(f"❌ Error updating signal statuses: {e}", exc_info=True)

        end_time = datetime.now(timezone.utc)
        duration = end_time - start_time
        logger.info(f"🎉 Strategy processing completed successfully in {duration.total_seconds():.2f} seconds")

    except Exception as e:
        end_time = datetime.now(timezone.utc)
        duration = end_time - start_time
        logger.error(f"💥 Error processing strategies after {duration.total_seconds():.2f} seconds: {e}", exc_info=True)

async def process_strategy(strategy):
    """
    Process a single trading strategy using AI-based signal generation

    Args:
        strategy (Strategy): Strategy to process
    """
    logger.info(f"Processing AI strategy: {strategy.name}")

    try:
        # Check if strategy has prompt (new AI-based) or code (legacy)
        if strategy.strategy_prompt:
            # Use AI-based signal generation
            logger.info(f"🤖 Using AI signal generation for strategy: {strategy.name}")
            logger.info(f"📝 Strategy prompt exists: {len(strategy.strategy_prompt)} characters")

            signals = await ai_signal_generator.generate_signals_for_strategy(strategy)
            logger.info(f"🎯 AI generator returned {len(signals)} signals")

            # Save generated signals to database
            saved_count = 0
            for signal_data in signals:
                try:
                    await save_ai_trading_signal(signal_data)
                    saved_count += 1
                    logger.info(f"💾 Saved signal {saved_count}/{len(signals)}: {signal_data['symbol']} {signal_data['direction']} at {signal_data['entry_price']}")
                except Exception as e:
                    logger.error(f"💥 Failed to save signal {signal_data.get('symbol', 'unknown')}: {e}")

            logger.info(f"✅ Generated and saved {saved_count}/{len(signals)} AI signals for strategy {strategy.name}")

        elif strategy.code:
            # Legacy code-based strategy (fallback)
            logger.info(f"Using legacy code-based processing for strategy: {strategy.name}")
            symbols = strategy.symbols.split(",")
            timeframes = strategy.timeframes.split(",")

            # Process each symbol and timeframe combination
            for symbol in symbols:
                for timeframe in timeframes:
                    await process_symbol_timeframe(strategy, symbol.strip(), timeframe.strip())
        else:
            logger.warning(f"Strategy {strategy.name} has neither prompt nor code - skipping")

    except Exception as e:
        logger.error(f"Error processing strategy {strategy.name}: {e}", exc_info=True)

async def process_symbol_timeframe(strategy, symbol, timeframe):
    """
    Process a strategy for a specific symbol and timeframe

    Args:
        strategy (Strategy): Strategy to process
        symbol (str): Symbol to process
        timeframe (str): Timeframe to process
    """
    logger.info(f"Processing {strategy.name} for {symbol} {timeframe}")

    try:
        # Get candle data
        candles = await get_candle_data(symbol, timeframe)

        if not candles:
            logger.warning(f"No candle data found for {symbol} {timeframe}")
            return

        if len(candles) < 50:  # Need enough data for indicators
            logger.warning(f"Not enough candle data for {symbol} {timeframe} (found {len(candles)}, need at least 50)")
            return

        try:
            # Convert to pandas DataFrame
            df = pd.DataFrame([
                {
                    "timestamp": candle.timestamp,
                    "open": candle.open,
                    "high": candle.high,
                    "low": candle.low,
                    "close": candle.close,
                    "volume": candle.volume if candle.volume else 0
                }
                for candle in candles
            ])

            # Check for NaN values
            if df.isnull().values.any():
                logger.warning(f"DataFrame contains NaN values for {symbol} {timeframe}")
                # Fill NaN values with appropriate methods (using new pandas syntax)
                df = df.ffill()  # Forward fill
                if df.isnull().values.any():
                    df = df.bfill()  # Backward fill if still have NaNs

            # Sort by timestamp
            df = df.sort_values("timestamp")

            # Check if we have enough data after sorting and cleaning
            if len(df) < 50:
                logger.warning(f"Not enough valid data for {symbol} {timeframe} after cleaning")
                return

            # Execute strategy
            signals = await execute_strategy(strategy, df, symbol, timeframe)

            if signals:
                # Save signals to database
                for signal in signals:
                    await save_trading_signal(strategy.id, signal)
        except Exception as e:
            logger.error(f"Error processing DataFrame for {symbol} {timeframe}: {e}", exc_info=True)
            return

    except Exception as e:
        logger.error(f"Error processing {strategy.name} for {symbol} {timeframe}: {e}", exc_info=True)

async def get_candle_data(symbol, timeframe):
    """
    Get candle data for a symbol and timeframe

    Args:
        symbol (str): Symbol to get data for
        timeframe (str): Timeframe to get data for

    Returns:
        list: List of CandleData objects
    """
    # Use a similar pattern to the one in visualization/chart_generator.py
    from src.database.setup import AsyncSessionLocal

    candles = []
    try:
        async for db in get_async_db():
            try:
                # Get candle data for the last 200 candles
                if AsyncSessionLocal is None:
                    # Synchronous operation for SQLite
                    result = db.execute(
                        select(CandleData)
                        .where(
                            CandleData.symbol == symbol,
                            CandleData.timeframe == timeframe
                        )
                        .order_by(CandleData.timestamp.desc())
                        .limit(200)
                    )
                else:
                    # Asynchronous operation for other databases
                    result = await db.execute(
                        select(CandleData)
                        .where(
                            CandleData.symbol == symbol,
                            CandleData.timeframe == timeframe
                        )
                        .order_by(CandleData.timestamp.desc())
                        .limit(200)
                    )

                candles = result.scalars().all()
                break  # Exit the loop if successful
            except Exception as e:
                logger.error(f"Error getting candle data for {symbol} {timeframe}: {e}", exc_info=True)
                # Continue to next iteration to try again with a new connection
    except Exception as e:
        logger.error(f"Error in get_candle_data for {symbol} {timeframe}: {e}", exc_info=True)

    return candles

async def get_fresh_candle_data_for_signal(signal):
    """
    Get fresh candle data directly from MT5 API for signal status updates

    Args:
        signal: TradingSignal object

    Returns:
        dict: Latest candle data or None if not available
    """
    from src.data_collection.market_data import get_mt5_candle_data

    try:
        # Convert symbol format for MT5 API (e.g., "BTC/USD" -> "BTCUSD")
        mt5_symbol = signal.symbol.replace("/", "").replace("-", "")

        logger.info(f"Fetching fresh candle data for signal {signal.id}: {mt5_symbol} {signal.timeframe}")

        # Get latest 2 candles to ensure we have the most recent data
        fresh_candles = await get_mt5_candle_data(mt5_symbol, signal.timeframe, 2)

        if fresh_candles and len(fresh_candles) > 0:
            # Return the latest candle
            latest_candle = fresh_candles[-1]
            logger.info(f"Fresh candle for {signal.symbol}: High={latest_candle.get('high')}, Low={latest_candle.get('low')}, Close={latest_candle.get('close')}")

            # Create a simple object with the required attributes for compatibility
            class FreshCandle:
                def __init__(self, data):
                    self.timestamp = data.get("timestamp")
                    self.open = data.get("open")
                    self.high = data.get("high")
                    self.low = data.get("low")
                    self.close = data.get("close")
                    self.volume = data.get("volume", 0)

            return FreshCandle(latest_candle)
        else:
            logger.warning(f"No fresh candle data received from MT5 API for {signal.symbol} {signal.timeframe}")
            return None

    except Exception as e:
        logger.error(f"Error fetching fresh candle data for signal {signal.id}: {e}", exc_info=True)
        return None

async def execute_strategy(strategy, df, symbol, timeframe):
    """
    Execute a trading strategy

    Args:
        strategy (Strategy): Strategy to execute
        df (pandas.DataFrame): Candle data
        symbol (str): Symbol being processed
        timeframe (str): Timeframe being processed

    Returns:
        list: List of trading signals
    """
    module_name = f"strategy_{strategy.id}"

    try:
        # Create a temporary module for the strategy code
        spec = importlib.util.spec_from_loader(module_name, loader=None)
        module = importlib.util.module_from_spec(spec)
        sys.modules[module_name] = module

        # Add necessary imports to the module
        exec("import pandas as pd", module.__dict__)
        exec("import numpy as np", module.__dict__)
        exec("import pandas_ta as ta", module.__dict__)

        try:
            # Execute the strategy code
            exec(strategy.code, module.__dict__)
        except SyntaxError as e:
            logger.error(f"Syntax error in strategy code for {strategy.name}: {e}", exc_info=True)
            return []
        except Exception as e:
            logger.error(f"Error executing strategy code for {strategy.name}: {e}", exc_info=True)
            return []

        # Check if the module has the required function
        if not hasattr(module, "generate_signals"):
            logger.error(f"Strategy {strategy.name} does not have a generate_signals function")
            return []

        try:
            # Call the generate_signals function
            signals = module.generate_signals(df, symbol, timeframe, strategy.parameters)

            # Validate signals
            if signals is None:
                logger.warning(f"Strategy {strategy.name} returned None instead of a list of signals")
                return []

            if not isinstance(signals, list):
                logger.warning(f"Strategy {strategy.name} returned {type(signals)} instead of a list of signals")
                return []

            # Return the signals
            return signals

        except Exception as e:
            logger.error(f"Error generating signals for {strategy.name}: {e}", exc_info=True)
            return []

    except Exception as e:
        logger.error(f"Error executing strategy {strategy.name}: {e}", exc_info=True)
        return []
    finally:
        # Make sure we always clean up the module
        if module_name in sys.modules:
            del sys.modules[module_name]

async def save_trading_signal(strategy_id, signal_data):
    """
    Save a trading signal to the database

    Args:
        strategy_id (int): ID of the strategy that generated the signal
        signal_data (dict): Signal data
    """
    async for db in get_async_db():
        try:
            # Enhanced duplicate detection - check for any active signal for same symbol-timeframe
            # regardless of strategy to prevent conflicts
            symbol = signal_data["symbol"]
            timeframe = signal_data["timeframe"]
            direction = signal_data["direction"]
            entry_price = float(signal_data["entry_price"])

            # Define active statuses that should prevent new signal generation
            active_statuses = [
                SignalStatus.ACTIVE,
                SignalStatus.TP1_HIT,
                SignalStatus.TP2_HIT,
                SignalStatus.TP3_HIT,
                SignalStatus.BREAK_EVEN
            ]

            # First check: Any active signal for same symbol-timeframe (across all strategies)
            if is_sqlite_db():
                symbol_timeframe_result = db.execute(
                    select(TradingSignal).where(
                        TradingSignal.symbol == symbol,
                        TradingSignal.timeframe == timeframe,
                        TradingSignal.status.in_(active_statuses),
                        TradingSignal.entry_time >= datetime.now(timezone.utc) - timedelta(hours=24)  # Extended to 24 hours
                    )
                )
            else:
                symbol_timeframe_result = await db.execute(
                    select(TradingSignal).where(
                        TradingSignal.symbol == symbol,
                        TradingSignal.timeframe == timeframe,
                        TradingSignal.status.in_(active_statuses),
                        TradingSignal.entry_time >= datetime.now(timezone.utc) - timedelta(hours=24)  # Extended to 24 hours
                    )
                )

            existing_symbol_timeframe_signal = symbol_timeframe_result.scalars().first()

            if existing_symbol_timeframe_signal:
                logger.info(f"🚫 Active signal already exists for {symbol} {timeframe} - Status: {existing_symbol_timeframe_signal.status.value}, Strategy: {existing_symbol_timeframe_signal.strategy_id}")
                logger.info(f"   Existing signal: {existing_symbol_timeframe_signal.direction} at {existing_symbol_timeframe_signal.entry_price}")
                logger.info(f"   New signal would be: {direction} at {entry_price}")
                return

            # Second check: Similar signal from same strategy (price-based duplicate detection)
            price_tolerance = entry_price * 0.002  # 0.2% tolerance for price comparison

            if is_sqlite_db():
                similar_signal_result = db.execute(
                    select(TradingSignal).where(
                        TradingSignal.strategy_id == strategy_id,
                        TradingSignal.symbol == symbol,
                        TradingSignal.direction == direction,
                        TradingSignal.entry_time >= datetime.now(timezone.utc) - timedelta(hours=12),  # 12 hours for same strategy
                        TradingSignal.status.in_(active_statuses),
                        TradingSignal.entry_price.between(entry_price - price_tolerance, entry_price + price_tolerance)
                    )
                )
            else:
                similar_signal_result = await db.execute(
                    select(TradingSignal).where(
                        TradingSignal.strategy_id == strategy_id,
                        TradingSignal.symbol == symbol,
                        TradingSignal.direction == direction,
                        TradingSignal.entry_time >= datetime.now(timezone.utc) - timedelta(hours=12),  # 12 hours for same strategy
                        TradingSignal.status.in_(active_statuses),
                        TradingSignal.entry_price.between(entry_price - price_tolerance, entry_price + price_tolerance)
                    )
                )

            existing_similar_signal = similar_signal_result.scalars().first()

            if existing_similar_signal:
                logger.info(f"🚫 Similar signal detected from same strategy for {symbol} {direction} at price {entry_price}")
                logger.info(f"   Existing: {existing_similar_signal.entry_price} (Status: {existing_similar_signal.status.value})")
                return

            # Create new trading signal with multi-TP support
            take_profit_1 = signal_data.get("take_profit_1") or signal_data.get("take_profit")
            take_profit_2 = signal_data.get("take_profit_2")
            take_profit_3 = signal_data.get("take_profit_3")

            trading_signal = TradingSignal(
                strategy_id=strategy_id,
                symbol=signal_data["symbol"],
                timeframe=signal_data["timeframe"],
                direction=signal_data["direction"],
                entry_price=signal_data["entry_price"],
                stop_loss=signal_data["stop_loss"],
                take_profit_1=take_profit_1,
                take_profit_2=take_profit_2,
                take_profit_3=take_profit_3,
                take_profit=take_profit_1,  # Legacy field
                risk_reward=abs((take_profit_1 - signal_data["entry_price"]) /
                               (signal_data["entry_price"] - signal_data["stop_loss"])),
                status=SignalStatus.ACTIVE,
                entry_time=datetime.now(timezone.utc),
                notes=signal_data.get("notes"),
                chart_image=signal_data.get("chart_image")
            )

            db.add(trading_signal)
            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()

            # Generate chart for the signal
            try:
                from src.visualization.chart_generator import generate_signal_chart
                chart_path = await generate_signal_chart(trading_signal)

                if chart_path:
                    logger.info(f"Chart generated successfully: {chart_path}")

                    # Update signal with chart image
                    trading_signal.chart_image = chart_path
                    if is_sqlite_db():
                        db.commit()
                    else:
                        await db.commit()

                    logger.info(f"Signal updated with chart image: {chart_path}")
                else:
                    logger.warning(f"Failed to generate chart for signal {trading_signal.id}")
            except Exception as chart_error:
                logger.error(f"Error generating chart for signal {trading_signal.id}: {chart_error}", exc_info=True)

            logger.info(f"Saved trading signal for {signal_data['symbol']} {signal_data['direction']}")

        except Exception as e:
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
            logger.error(f"Error saving trading signal: {e}", exc_info=True)
            raise

async def process_multi_tp_signal(signal, latest_candle, db):
    """Process a signal with multiple TP levels"""
    logger.info(f"🔍 Processing multi-TP signal {signal.id} ({signal.symbol} {signal.direction.upper()})")

    # Get current TP levels
    tp_levels = []
    if signal.take_profit_1:
        tp_levels.append((1, signal.take_profit_1))
    if signal.take_profit_2:
        tp_levels.append((2, signal.take_profit_2))
    if signal.take_profit_3:
        tp_levels.append((3, signal.take_profit_3))

    logger.info(f"   Entry: {signal.entry_price}, SL: {signal.stop_loss}")
    logger.info(f"   TP Levels: {tp_levels}")
    logger.info(f"   Candle: High={latest_candle.high}, Low={latest_candle.low}, Close={latest_candle.close}")

    # Enhanced SL hit detection with precision tolerance and validation
    sl_hit = False

    # Add small tolerance for price precision issues (0.1% of entry price or 1 pip, whichever is smaller)
    entry_price = signal.entry_price
    price_tolerance = min(entry_price * 0.001, 0.0001)  # 0.1% or 1 pip tolerance

    # Validate candle data quality first
    if not latest_candle or not hasattr(latest_candle, 'high') or not hasattr(latest_candle, 'low'):
        logger.warning(f"⚠️ Invalid candle data for signal {signal.id}, skipping SL check")
        return False

    if latest_candle.high <= 0 or latest_candle.low <= 0 or latest_candle.high < latest_candle.low:
        logger.warning(f"⚠️ Suspicious candle data for signal {signal.id}: High={latest_candle.high}, Low={latest_candle.low}")
        return False

    # Check for stop loss hit with tolerance
    if signal.direction == "buy":
        # For BUY signals, SL is hit when price goes below SL level (with tolerance)
        sl_hit = latest_candle.low <= (signal.stop_loss + price_tolerance)
        if sl_hit:
            logger.info(f"🔍 BUY SL Check: Candle Low={latest_candle.low}, SL={signal.stop_loss}, Tolerance={price_tolerance}")
    else:  # sell
        # For SELL signals, SL is hit when price goes above SL level (with tolerance)
        sl_hit = latest_candle.high >= (signal.stop_loss - price_tolerance)
        if sl_hit:
            logger.info(f"🔍 SELL SL Check: Candle High={latest_candle.high}, SL={signal.stop_loss}, Tolerance={price_tolerance}")

    if sl_hit:
        # Additional validation: Check if the signal was just created (avoid immediate SL hits due to data issues)
        # Ensure both datetimes are timezone-aware for comparison
        now_utc = datetime.now(timezone.utc)
        entry_time = signal.entry_time
        if entry_time.tzinfo is None:
            entry_time = entry_time.replace(tzinfo=timezone.utc)
        else:
            entry_time = entry_time.astimezone(timezone.utc)

        signal_age_minutes = (now_utc - entry_time).total_seconds() / 60
        if signal_age_minutes < 5:  # Signal is less than 5 minutes old
            logger.warning(f"⚠️ Signal {signal.id} would hit SL immediately (age: {signal_age_minutes:.1f} min), possible data issue - skipping SL")
            return False

        # Additional validation: Check if SL is reasonable compared to entry price
        sl_distance_percent = abs(signal.stop_loss - entry_price) / entry_price * 100
        if sl_distance_percent > 10:  # SL is more than 10% away from entry
            logger.warning(f"⚠️ Signal {signal.id} has unusually large SL distance ({sl_distance_percent:.2f}%), validating...")

        # Check if this is a break-even scenario (TP1 hit and SL moved to entry price)
        if signal.break_even_triggered and abs(signal.stop_loss - signal.entry_price) < price_tolerance:
            signal.status = SignalStatus.BREAK_EVEN
            signal.exit_time = latest_candle.timestamp
            signal.exit_price = signal.entry_price  # Exit at entry price for break-even
            signal.remaining_position_size = 0.0
            signal.profit_loss = 0.0  # Break-even means 0% profit/loss
            logger.info(f"⚖️ Signal {signal.id} closed at break-even (entry price: {signal.entry_price}) - P&L: 0.00% (Age: {signal_age_minutes:.1f} min)")
        else:
            signal.status = SignalStatus.SL_HIT
            signal.exit_time = latest_candle.timestamp
            signal.exit_price = signal.stop_loss
            signal.remaining_position_size = 0.0

            if signal.direction == "buy":
                signal.profit_loss = (signal.stop_loss - signal.entry_price) / signal.entry_price * 100
            else:
                signal.profit_loss = (signal.entry_price - signal.stop_loss) / signal.entry_price * 100

            logger.info(f"🛑 Signal {signal.id} hit SL at {signal.stop_loss} - P&L: {signal.profit_loss:.2f}% (Age: {signal_age_minutes:.1f} min)")

        return True

    # Check TP levels in order
    new_tp_hits = []
    for tp_level, tp_price in tp_levels:
        # Check if this TP was already hit
        if is_sqlite_db():
            existing_hit = db.execute(
                select(TakeProfitHit).where(
                    TakeProfitHit.signal_id == signal.id,
                    TakeProfitHit.tp_level == tp_level
                )
            ).scalars().first()
        else:
            existing_hit = (await db.execute(
                select(TakeProfitHit).where(
                    TakeProfitHit.signal_id == signal.id,
                    TakeProfitHit.tp_level == tp_level
                )
            )).scalars().first()

        if existing_hit:
            continue  # Already hit

        # Check if TP was hit
        tp_hit = False
        if signal.direction == "buy":
            tp_hit = latest_candle.high >= tp_price
        else:  # sell
            tp_hit = latest_candle.low <= tp_price

        if tp_hit:
            # Calculate position closed percentage (33% for each TP level)
            position_closed = 33.33 if len(tp_levels) == 3 else (50.0 if len(tp_levels) == 2 else 100.0)

            # Calculate P&L for this TP level
            if signal.direction == "buy":
                tp_profit_loss = (tp_price - signal.entry_price) / signal.entry_price * 100
            else:
                tp_profit_loss = (signal.entry_price - tp_price) / signal.entry_price * 100

            # Create TP hit record
            tp_hit_record = TakeProfitHit(
                signal_id=signal.id,
                tp_level=tp_level,
                tp_price=tp_price,
                hit_time=latest_candle.timestamp,
                hit_price=tp_price,
                position_closed_percentage=position_closed,
                profit_loss_percentage=tp_profit_loss,
                created_at=datetime.now(timezone.utc)
            )

            db.add(tp_hit_record)
            new_tp_hits.append((tp_level, tp_price, tp_profit_loss))

            # Update signal status
            if tp_level == 1:
                signal.status = SignalStatus.TP1_HIT
                # Move SL to break even after TP1
                signal.stop_loss = signal.entry_price
                signal.break_even_triggered = True
                logger.info(f"🎯 TP1 hit! Moving SL to break even: {signal.entry_price}")
            elif tp_level == 2:
                signal.status = SignalStatus.TP2_HIT
            elif tp_level == 3:
                signal.status = SignalStatus.TP3_HIT
                signal.status = SignalStatus.ALL_TP_HIT
                signal.exit_time = latest_candle.timestamp
                signal.exit_price = tp_price
                signal.remaining_position_size = 0.0

            # Update remaining position
            signal.remaining_position_size = max(0.0, signal.remaining_position_size - position_closed)

            logger.info(f"🎯 Signal {signal.id} hit TP{tp_level} at {tp_price} - P&L: {tp_profit_loss:.2f}% (Position closed: {position_closed}%)")

    return len(new_tp_hits) > 0

async def update_signal_statuses():
    """Update the status of active trading signals"""
    logger.info("Updating signal statuses")

    async for db in get_async_db():
        try:
            # Get all active signals
            if is_sqlite_db():
                result = db.execute(
                    select(TradingSignal).where(TradingSignal.status == SignalStatus.ACTIVE)
                )
            else:
                result = await db.execute(
                    select(TradingSignal).where(TradingSignal.status == SignalStatus.ACTIVE)
                )
            active_signals = result.scalars().all()

            if not active_signals:
                logger.info("No active signals to update")
                return

            logger.info(f"Updating {len(active_signals)} active signals")

            # Update each signal
            for signal in active_signals:
                # Get fresh candle data directly from MT5 API
                latest_candle = await get_fresh_candle_data_for_signal(signal)

                if not latest_candle:
                    logger.warning(f"No fresh candle data available for signal {signal.id} ({signal.symbol} {signal.timeframe})")
                    continue

                # Check if signal has multi-TP levels
                old_status = signal.status

                # Use multi-TP processing if signal has TP levels
                if signal.take_profit_1:
                    signal_updated = await process_multi_tp_signal(signal, latest_candle, db)
                    if signal_updated:
                        logger.info(f"📊 Signal {signal.id} status updated from {old_status.value} to {signal.status.value}")
                        continue

                # Legacy single-TP logic for backward compatibility
                logger.info(f"🔄 Using legacy single-TP logic for signal {signal.id}")
                tp_hit = False
                sl_hit = False

                logger.info(f"🔍 Checking signal {signal.id} ({signal.symbol} {signal.direction.upper()})")
                logger.info(f"   Entry: {signal.entry_price}, TP: {signal.take_profit}, SL: {signal.stop_loss}")
                logger.info(f"   Candle: High={latest_candle.high}, Low={latest_candle.low}, Close={latest_candle.close}")

                # Add precision tolerance for legacy signals too
                entry_price = signal.entry_price
                price_tolerance = min(entry_price * 0.001, 0.0001)  # 0.1% or 1 pip tolerance

                # Validate candle data quality
                if not latest_candle or latest_candle.high <= 0 or latest_candle.low <= 0 or latest_candle.high < latest_candle.low:
                    logger.warning(f"⚠️ Invalid candle data for legacy signal {signal.id}, skipping update")
                    continue

                # Check signal age to avoid immediate hits due to data issues
                # Ensure both datetimes are timezone-aware for comparison
                now_utc = datetime.now(timezone.utc)
                entry_time = signal.entry_time
                if entry_time.tzinfo is None:
                    entry_time = entry_time.replace(tzinfo=timezone.utc)
                else:
                    entry_time = entry_time.astimezone(timezone.utc)

                signal_age_minutes = (now_utc - entry_time).total_seconds() / 60

                if signal.direction == "buy":
                    tp_hit = latest_candle.high >= signal.take_profit
                    sl_hit = latest_candle.low <= (signal.stop_loss + price_tolerance)

                    # Skip immediate SL hits for new signals
                    if sl_hit and signal_age_minutes < 5:
                        logger.warning(f"⚠️ Legacy BUY signal {signal.id} would hit SL immediately (age: {signal_age_minutes:.1f} min), skipping")
                        sl_hit = False

                    logger.info(f"   BUY signal: TP hit={tp_hit} (high {latest_candle.high} >= {signal.take_profit}), SL hit={sl_hit} (low {latest_candle.low} <= {signal.stop_loss + price_tolerance})")

                    # Determine which was hit first based on price movement
                    if tp_hit and sl_hit:
                        # Both levels hit in same candle - determine based on entry price and current close
                        # If close is closer to TP, assume TP was hit last (SL hit first)
                        # If close is closer to SL, assume SL was hit last (TP hit first)
                        distance_to_tp = abs(latest_candle.close - signal.take_profit)
                        distance_to_sl = abs(latest_candle.close - signal.stop_loss)

                        if distance_to_sl < distance_to_tp:
                            # Close is closer to SL, so SL was likely hit last
                            # Check if this is a break-even scenario
                            if signal.break_even_triggered and abs(signal.stop_loss - signal.entry_price) < price_tolerance:
                                signal.status = SignalStatus.BREAK_EVEN
                                signal.exit_time = latest_candle.timestamp
                                signal.exit_price = signal.entry_price
                                signal.profit_loss = 0.0
                                logger.info(f"⚖️ Signal {signal.id} ({signal.symbol} BUY) closed at break-even - P&L: 0.00% (both levels hit, break-even triggered)")
                            else:
                                signal.status = SignalStatus.SL_HIT
                                signal.exit_time = latest_candle.timestamp
                                signal.exit_price = signal.stop_loss
                                signal.profit_loss = (signal.stop_loss - signal.entry_price) / signal.entry_price * 100
                                logger.info(f"🛑 Signal {signal.id} ({signal.symbol} BUY) hit SL at {signal.stop_loss} - P&L: {signal.profit_loss:.2f}% (both levels hit, SL closer to close)")
                        else:
                            # Close is closer to TP, so TP was likely hit last
                            signal.status = SignalStatus.TP_HIT
                            signal.exit_time = latest_candle.timestamp
                            signal.exit_price = signal.take_profit
                            signal.profit_loss = (signal.take_profit - signal.entry_price) / signal.entry_price * 100
                            logger.info(f"🎯 Signal {signal.id} ({signal.symbol} BUY) hit TP at {signal.take_profit} - P&L: {signal.profit_loss:.2f}% (both levels hit, TP closer to close)")
                    elif tp_hit:
                        signal.status = SignalStatus.TP_HIT
                        signal.exit_time = latest_candle.timestamp
                        signal.exit_price = signal.take_profit
                        signal.profit_loss = (signal.take_profit - signal.entry_price) / signal.entry_price * 100
                        logger.info(f"🎯 Signal {signal.id} ({signal.symbol} BUY) hit TP at {signal.take_profit} - P&L: {signal.profit_loss:.2f}%")
                    elif sl_hit:
                        # Check if this is a break-even scenario
                        if signal.break_even_triggered and abs(signal.stop_loss - signal.entry_price) < price_tolerance:
                            signal.status = SignalStatus.BREAK_EVEN
                            signal.exit_time = latest_candle.timestamp
                            signal.exit_price = signal.entry_price
                            signal.profit_loss = 0.0
                            logger.info(f"⚖️ Signal {signal.id} ({signal.symbol} BUY) closed at break-even - P&L: 0.00%")
                        else:
                            signal.status = SignalStatus.SL_HIT
                            signal.exit_time = latest_candle.timestamp
                            signal.exit_price = signal.stop_loss
                            signal.profit_loss = (signal.stop_loss - signal.entry_price) / signal.entry_price * 100
                            logger.info(f"🛑 Signal {signal.id} ({signal.symbol} BUY) hit SL at {signal.stop_loss} - P&L: {signal.profit_loss:.2f}%")

                else:  # sell
                    tp_hit = latest_candle.low <= signal.take_profit
                    sl_hit = latest_candle.high >= (signal.stop_loss - price_tolerance)

                    # Skip immediate SL hits for new signals
                    if sl_hit and signal_age_minutes < 5:
                        logger.warning(f"⚠️ Legacy SELL signal {signal.id} would hit SL immediately (age: {signal_age_minutes:.1f} min), skipping")
                        sl_hit = False

                    logger.info(f"   SELL signal: TP hit={tp_hit} (low {latest_candle.low} <= {signal.take_profit}), SL hit={sl_hit} (high {latest_candle.high} >= {signal.stop_loss - price_tolerance})")

                    # Determine which was hit first based on price movement
                    if tp_hit and sl_hit:
                        # Both levels hit in same candle - determine based on entry price and current close
                        distance_to_tp = abs(latest_candle.close - signal.take_profit)
                        distance_to_sl = abs(latest_candle.close - signal.stop_loss)

                        if distance_to_sl < distance_to_tp:
                            # Close is closer to SL, so SL was likely hit last
                            # Check if this is a break-even scenario
                            if signal.break_even_triggered and abs(signal.stop_loss - signal.entry_price) < price_tolerance:
                                signal.status = SignalStatus.BREAK_EVEN
                                signal.exit_time = latest_candle.timestamp
                                signal.exit_price = signal.entry_price
                                signal.profit_loss = 0.0
                                logger.info(f"⚖️ Signal {signal.id} ({signal.symbol} SELL) closed at break-even - P&L: 0.00% (both levels hit, break-even triggered)")
                            else:
                                signal.status = SignalStatus.SL_HIT
                                signal.exit_time = latest_candle.timestamp
                                signal.exit_price = signal.stop_loss
                                signal.profit_loss = (signal.entry_price - signal.stop_loss) / signal.entry_price * 100
                                logger.info(f"🛑 Signal {signal.id} ({signal.symbol} SELL) hit SL at {signal.stop_loss} - P&L: {signal.profit_loss:.2f}% (both levels hit, SL closer to close)")
                        else:
                            # Close is closer to TP, so TP was likely hit last
                            signal.status = SignalStatus.TP_HIT
                            signal.exit_time = latest_candle.timestamp
                            signal.exit_price = signal.take_profit
                            signal.profit_loss = (signal.entry_price - signal.take_profit) / signal.entry_price * 100
                            logger.info(f"🎯 Signal {signal.id} ({signal.symbol} SELL) hit TP at {signal.take_profit} - P&L: {signal.profit_loss:.2f}% (both levels hit, TP closer to close)")
                    elif tp_hit:
                        signal.status = SignalStatus.TP_HIT
                        signal.exit_time = latest_candle.timestamp
                        signal.exit_price = signal.take_profit
                        signal.profit_loss = (signal.entry_price - signal.take_profit) / signal.entry_price * 100
                        logger.info(f"🎯 Signal {signal.id} ({signal.symbol} SELL) hit TP at {signal.take_profit} - P&L: {signal.profit_loss:.2f}%")
                    elif sl_hit:
                        # Check if this is a break-even scenario
                        if signal.break_even_triggered and abs(signal.stop_loss - signal.entry_price) < price_tolerance:
                            signal.status = SignalStatus.BREAK_EVEN
                            signal.exit_time = latest_candle.timestamp
                            signal.exit_price = signal.entry_price
                            signal.profit_loss = 0.0
                            logger.info(f"⚖️ Signal {signal.id} ({signal.symbol} SELL) closed at break-even - P&L: 0.00%")
                        else:
                            signal.status = SignalStatus.SL_HIT
                            signal.exit_time = latest_candle.timestamp
                            signal.exit_price = signal.stop_loss
                            signal.profit_loss = (signal.entry_price - signal.stop_loss) / signal.entry_price * 100
                            logger.info(f"🛑 Signal {signal.id} ({signal.symbol} SELL) hit SL at {signal.stop_loss} - P&L: {signal.profit_loss:.2f}%")

                # Log status changes
                if old_status != signal.status:
                    logger.info(f"📊 Signal {signal.id} status changed from {old_status.value} to {signal.status.value}")

                # Check if signal is expired (more than 7 days old)
                # Make sure both datetimes are timezone-aware for comparison
                now = datetime.now(timezone.utc)
                entry_time = signal.entry_time

                # Ensure entry_time is timezone-aware for comparison
                if entry_time.tzinfo is None:
                    entry_time = entry_time.replace(tzinfo=timezone.utc)
                else:
                    entry_time = entry_time.astimezone(timezone.utc)

                if signal.status == SignalStatus.ACTIVE and entry_time < now - timedelta(days=7):
                    signal.status = SignalStatus.EXPIRED
                    signal.exit_time = now
                    signal.exit_price = latest_candle.close

                    if signal.direction == "buy":
                        signal.profit_loss = (latest_candle.close - signal.entry_price) / signal.entry_price * 100
                    else:  # sell
                        signal.profit_loss = (signal.entry_price - latest_candle.close) / signal.entry_price * 100

            # Commit changes
            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()

            logger.info("Signal statuses updated")

        except Exception as e:
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
            logger.error(f"Error updating signal statuses: {e}", exc_info=True)
            raise

async def save_ai_trading_signal(signal_data):
    """
    Save AI-generated trading signal to database

    Args:
        signal_data (dict): Signal data from AI generator
    """
    try:
        logger.info(f"💾 Saving AI trading signal to database...")
        logger.info(f"📊 Signal data: {signal_data}")

        async for db in get_async_db():
            try:
                # Enhanced duplicate detection for AI signals - same logic as regular signals
                symbol = signal_data["symbol"]
                timeframe = signal_data["timeframe"]
                direction = signal_data["direction"]
                entry_price = float(signal_data["entry_price"])
                strategy_id = signal_data["strategy_id"]

                # Define active statuses that should prevent new signal generation
                active_statuses = [
                    SignalStatus.ACTIVE,
                    SignalStatus.TP1_HIT,
                    SignalStatus.TP2_HIT,
                    SignalStatus.TP3_HIT,
                    SignalStatus.BREAK_EVEN
                ]

                # First check: Any active signal for same symbol-timeframe (across all strategies)
                if is_sqlite_db():
                    symbol_timeframe_result = db.execute(
                        select(TradingSignal).where(
                            TradingSignal.symbol == symbol,
                            TradingSignal.timeframe == timeframe,
                            TradingSignal.status.in_(active_statuses),
                            TradingSignal.entry_time >= datetime.now(timezone.utc) - timedelta(hours=24)
                        )
                    )
                else:
                    symbol_timeframe_result = await db.execute(
                        select(TradingSignal).where(
                            TradingSignal.symbol == symbol,
                            TradingSignal.timeframe == timeframe,
                            TradingSignal.status.in_(active_statuses),
                            TradingSignal.entry_time >= datetime.now(timezone.utc) - timedelta(hours=24)
                        )
                    )

                existing_symbol_timeframe_signal = symbol_timeframe_result.scalars().first()

                if existing_symbol_timeframe_signal:
                    logger.info(f"🚫 AI Signal blocked - Active signal already exists for {symbol} {timeframe}")
                    logger.info(f"   Existing: {existing_symbol_timeframe_signal.direction} at {existing_symbol_timeframe_signal.entry_price} (Status: {existing_symbol_timeframe_signal.status.value})")
                    logger.info(f"   Blocked: {direction} at {entry_price}")
                    return

                # Second check: Similar signal from same strategy (price-based duplicate detection)
                price_tolerance = entry_price * 0.002  # 0.2% tolerance

                if is_sqlite_db():
                    similar_signal_result = db.execute(
                        select(TradingSignal).where(
                            TradingSignal.strategy_id == strategy_id,
                            TradingSignal.symbol == symbol,
                            TradingSignal.direction == direction,
                            TradingSignal.entry_time >= datetime.now(timezone.utc) - timedelta(hours=12),
                            TradingSignal.status.in_(active_statuses),
                            TradingSignal.entry_price.between(entry_price - price_tolerance, entry_price + price_tolerance)
                        )
                    )
                else:
                    similar_signal_result = await db.execute(
                        select(TradingSignal).where(
                            TradingSignal.strategy_id == strategy_id,
                            TradingSignal.symbol == symbol,
                            TradingSignal.direction == direction,
                            TradingSignal.entry_time >= datetime.now(timezone.utc) - timedelta(hours=12),
                            TradingSignal.status.in_(active_statuses),
                            TradingSignal.entry_price.between(entry_price - price_tolerance, entry_price + price_tolerance)
                        )
                    )

                existing_similar_signal = similar_signal_result.scalars().first()

                if existing_similar_signal:
                    logger.info(f"🚫 AI Signal blocked - Similar signal from same strategy for {symbol} {direction} at {entry_price}")
                    logger.info(f"   Existing: {existing_similar_signal.entry_price} (Status: {existing_similar_signal.status.value})")
                    return

                # Calculate risk-reward ratio
                risk_reward = abs((signal_data["take_profit"] - signal_data["entry_price"]) /
                                 (signal_data["entry_price"] - signal_data["stop_loss"]))

                logger.info(f"📈 Calculated risk-reward ratio: {risk_reward:.2f}")

                # Create new trading signal with multi-TP support
                trading_signal = TradingSignal(
                    strategy_id=signal_data["strategy_id"],
                    symbol=signal_data["symbol"],
                    timeframe=signal_data["timeframe"],
                    direction=signal_data["direction"],
                    entry_price=signal_data["entry_price"],
                    stop_loss=signal_data["stop_loss"],
                    take_profit=signal_data.get("take_profit"),  # Legacy field for backward compatibility
                    take_profit_1=signal_data.get("take_profit_1"),
                    take_profit_2=signal_data.get("take_profit_2"),
                    take_profit_3=signal_data.get("take_profit_3"),
                    initial_position_size=100.0,  # Start with 100% position
                    remaining_position_size=100.0,  # Start with 100% remaining
                    break_even_triggered=False,  # Not triggered initially
                    risk_reward=risk_reward,
                    status=SignalStatus.ACTIVE,
                    entry_time=signal_data["signal_time"],
                    notes=signal_data.get("reasoning", "AI generated signal"),
                    ai_analysis=signal_data.get("reasoning", ""),
                    created_at=datetime.now(timezone.utc)
                )

                logger.info(f"🏗️ Created TradingSignal object: ID will be assigned after commit")
                logger.info(f"📋 Signal details: {signal_data['symbol']} {signal_data['direction']} @ {signal_data['entry_price']}")

                # Log multi-TP details
                tp_info = []
                if signal_data.get("take_profit_1"):
                    tp_info.append(f"TP1: {signal_data['take_profit_1']}")
                if signal_data.get("take_profit_2"):
                    tp_info.append(f"TP2: {signal_data['take_profit_2']}")
                if signal_data.get("take_profit_3"):
                    tp_info.append(f"TP3: {signal_data['take_profit_3']}")

                if tp_info:
                    logger.info(f"🎯 Multi-TP levels: {', '.join(tp_info)}")
                else:
                    logger.info(f"🎯 Legacy TP: {signal_data.get('take_profit', 'N/A')}")

                db.add(trading_signal)
                logger.info(f"➕ Added signal to database session")

                if is_sqlite_db():
                    db.commit()
                    logger.info(f"✅ SQLite commit successful")
                else:
                    await db.commit()
                    logger.info(f"✅ Async commit successful")

                logger.info(f"🎯 Signal saved with ID: {trading_signal.id}")

                # Generate chart for the signal
                try:
                    logger.info(f"📊 Attempting to generate chart for signal...")
                    from src.visualization.chart_generator import generate_signal_chart
                    chart_path = await generate_signal_chart(trading_signal)

                    if chart_path:
                        logger.info(f"📈 Chart generated successfully: {chart_path}")

                        # Update signal with chart image
                        trading_signal.chart_image = chart_path
                        if is_sqlite_db():
                            db.commit()
                        else:
                            await db.commit()

                        logger.info(f"🖼️ AI signal updated with chart image: {chart_path}")
                    else:
                        logger.warning(f"⚠️ Failed to generate chart for AI signal {trading_signal.id}")
                except Exception as chart_error:
                    logger.error(f"💥 Error generating chart for AI signal {trading_signal.id}: {chart_error}", exc_info=True)

                logger.info(f"✅ Successfully saved AI trading signal for {signal_data['symbol']} {signal_data['direction']} (confidence: {signal_data.get('confidence', 'N/A')})")
                break

            except Exception as e:
                logger.error(f"💥 Database error while saving AI trading signal: {e}")
                if is_sqlite_db():
                    db.rollback()
                    logger.info(f"🔄 SQLite rollback completed")
                else:
                    await db.rollback()
                    logger.info(f"🔄 Async rollback completed")
                raise

    except Exception as e:
        logger.error(f"💥 Critical error in save_ai_trading_signal: {e}", exc_info=True)
