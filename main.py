#!/usr/bin/env python3
"""
MignalyBot - AI-Powered Trading & Analysis Content Creator
Main application entry point
"""

import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
import os
from src.utils.logging_config import setup_logging
logger = setup_logging(log_level=os.getenv("LOG_LEVEL", "INFO"))

async def main():
    """Main application entry point"""
    logger.info("Starting MignalyBot")

    # Import components here to avoid circular imports
    from src.database.setup import init_db
    from src.admin.server import start_admin_server
    from src.telegram.bot import start_telegram_bot
    from src.data_collection.scheduler import start_data_collection
    from src.ai_integration.event_result_scheduler import run_event_result_scheduler
    from src.ai_integration.content_scheduler import start_greeting_scheduler
    from src.ai_integration.weekly_performance_scheduler import start_weekly_performance_scheduler
    from src.ai_integration.monthly_performance_scheduler import start_monthly_performance_scheduler
    # REMOVED: old content_scheduler - Content generation now happens automatically after data collection
    # ADDED: greeting_scheduler - Greeting posts are sent at 9 AM Tehran time separately
    # ADDED: weekly_performance_scheduler - Weekly performance reports sent every Saturday at 10 AM
    # ADDED: monthly_performance_scheduler - Monthly performance reports sent on 1st of each month at 11 AM

    # Initialize database
    await init_db()

    # Start components
    admin_task = asyncio.create_task(start_admin_server())
    telegram_task = asyncio.create_task(start_telegram_bot())
    data_collection_task = asyncio.create_task(start_data_collection())
    event_result_task = asyncio.create_task(run_event_result_scheduler())
    greeting_scheduler_task = asyncio.create_task(start_greeting_scheduler())  # NEW: Greeting posts at 9 AM Tehran time
    weekly_performance_task = asyncio.create_task(start_weekly_performance_scheduler())  # NEW: Weekly performance reports every Saturday at 10 AM
    monthly_performance_task = asyncio.create_task(start_monthly_performance_scheduler())  # NEW: Monthly performance reports on 1st of month at 11 AM
    # REMOVED: content_scheduler_task - Content generation now happens automatically after data collection every 6 hours

    # Wait for all tasks
    await asyncio.gather(
        admin_task,
        telegram_task,
        data_collection_task,
        event_result_task,
        greeting_scheduler_task,
        weekly_performance_task,
        monthly_performance_task
    )

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application stopped by user")
    except Exception as e:
        logger.error(f"Application error: {e}", exc_info=True)
