#!/bin/bash

# Docker script to inject Afghanistan Farsi prompts into MignalyBot database
# This script should be run from the host machine to execute the injection inside the Docker container

set -e

echo "🚀 Starting Afghanistan Farsi prompts injection into Docker container..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Find the MignalyBot container
CONTAINER_NAME="mignalybot"
CONTAINER_ID=$(docker ps -q -f name=$CONTAINER_NAME)

if [ -z "$CONTAINER_ID" ]; then
    echo "❌ MignalyBot container not found. Please make sure the container is running."
    echo "   Try: docker ps to see running containers"
    exit 1
fi

echo "✅ Found MignalyBot container: $CONTAINER_ID"

# Copy the injection script to the container
echo "📋 Copying injection script to container..."
docker cp inject_afghanistan_prompts.py $CONTAINER_ID:/app/inject_afghanistan_prompts.py

# Make the script executable
docker exec $CONTAINER_ID chmod +x /app/inject_afghanistan_prompts.py

# Execute the injection script inside the container
echo "🔧 Executing prompts injection inside container..."
docker exec $CONTAINER_ID python /app/inject_afghanistan_prompts.py

# Clean up - remove the script from container
echo "🧹 Cleaning up..."
docker exec $CONTAINER_ID rm -f /app/inject_afghanistan_prompts.py

echo "🎉 Afghanistan Farsi prompts injection completed!"
echo "📝 The fa-af language prompts should now be available in the database."
echo "🔄 You may need to restart the application or wait for the next prompt request to see the changes."
