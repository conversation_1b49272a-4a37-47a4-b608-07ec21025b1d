"""
Monthly performance report scheduler for MignalyBot
Automatically generates monthly performance reports on the first day of each month
"""

import asyncio
import logging
from datetime import datetime, time, timedelta
import pytz
from sqlalchemy import select, and_, func
from typing import Dict, List, Any

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import Config, Channel, TradingSignal, SignalStatus, TakeProfitHit
from src.ai_integration.qwen_client import QwenClient
# Import will be done dynamically to avoid circular imports
from src.utils.helpers import get_timezone, get_current_time, get_utc_time

logger = logging.getLogger(__name__)

async def monthly_performance_scheduler():
    """
    Scheduler that runs monthly performance report generation on the first day of each month
    """
    logger.info("Starting monthly performance report scheduler")

    while True:
        try:
            # Get current time in configured timezone
            now_local = get_current_time()
            
            # Calculate next first day of month at 11 AM
            if now_local.day == 1 and now_local.time() >= time(11, 0):
                # If it's the first day and past 11 AM, schedule for next month
                if now_local.month == 12:
                    next_month = now_local.replace(year=now_local.year + 1, month=1, day=1)
                else:
                    next_month = now_local.replace(month=now_local.month + 1, day=1)
            else:
                # Calculate first day of current month if we haven't passed 11 AM yet
                if now_local.day == 1 and now_local.time() < time(11, 0):
                    next_month = now_local.replace(day=1)
                else:
                    # Calculate first day of next month
                    if now_local.month == 12:
                        next_month = now_local.replace(year=now_local.year + 1, month=1, day=1)
                    else:
                        next_month = now_local.replace(month=now_local.month + 1, day=1)
            
            next_run = next_month.replace(hour=11, minute=0, second=0, microsecond=0)
            
            # Calculate seconds until next run
            time_until_run = (next_run - now_local).total_seconds()

            logger.info(f"Next monthly performance report scheduled for: {next_run.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            logger.info(f"Time until next run: {time_until_run / 3600:.2f} hours")

            # Wait until the scheduled time
            await asyncio.sleep(time_until_run)

            # Run monthly performance report generation
            logger.info(f"Running monthly performance report generation on first day of month at 11 AM {get_timezone().zone} time")
            reports_sent = await generate_monthly_performance_reports()
            logger.info(f"Monthly performance report generation completed. Sent {reports_sent} reports.")

        except Exception as e:
            logger.error(f"Error in monthly performance scheduler: {e}", exc_info=True)
            # Wait 1 hour before retrying on error
            await asyncio.sleep(3600)

async def generate_monthly_performance_reports():
    """
    Generate and send monthly performance reports for all active channels
    """
    reports_sent = 0
    
    try:
        async for db in get_async_db():
            # Get all active channels
            channels_query = select(Channel).where(Channel.active == True)
            if is_sqlite_db():
                channels_result = db.execute(channels_query)
            else:
                channels_result = await db.execute(channels_query)
            
            channels = channels_result.scalars().all()
            
            if not channels:
                logger.info("No active channels found for monthly reports")
                return 0
            
            # Calculate date range for last month (first day to last day)
            now_local = get_current_time()
            
            # Get first day of last month
            if now_local.month == 1:
                last_month_start = now_local.replace(year=now_local.year - 1, month=12, day=1)
            else:
                last_month_start = now_local.replace(month=now_local.month - 1, day=1)
            last_month_start = last_month_start.replace(hour=0, minute=0, second=0, microsecond=0)
            
            # Get last day of last month
            if now_local.month == 1:
                last_month_end = now_local.replace(year=now_local.year - 1, month=12, day=31)
            else:
                # Calculate last day of previous month
                first_day_current_month = now_local.replace(day=1)
                last_month_end = first_day_current_month - timedelta(days=1)
            last_month_end = last_month_end.replace(hour=23, minute=59, second=59, microsecond=999999)
            
            # Convert to UTC for database queries
            last_month_start_utc = last_month_start.astimezone(pytz.UTC)
            last_month_end_utc = last_month_end.astimezone(pytz.UTC)
            
            logger.info(f"Generating monthly reports for period: {last_month_start.strftime('%Y-%m-%d')} to {last_month_end.strftime('%Y-%m-%d')}")
            
            # Get performance statistics for the month
            stats = await get_monthly_performance_stats(db, last_month_start_utc, last_month_end_utc)
            
            if not stats['total_signals']:
                logger.info("No signals found for the past month")
                return 0
            
            # Initialize Qwen client
            qwen_client = QwenClient()
            
            # Generate reports for each channel
            for channel in channels:
                try:
                    # Check if performance posts are enabled for this channel
                    post_types = set(channel.post_types.split(",")) if channel.post_types else set()
                    
                    if "performance" not in post_types:
                        logger.info(f"Performance posts disabled for channel {channel.name}, skipping monthly report")
                        continue
                    
                    # Generate monthly report content
                    report_content = await generate_monthly_report_content(
                        stats, 
                        last_month_start, 
                        last_month_end, 
                        channel.language, 
                        channel.brand_name,
                        qwen_client
                    )
                    
                    if report_content:
                        # Send the report (import dynamically to avoid circular imports)
                        try:
                            from src.telegram.bot import send_message_to_channel
                            await send_message_to_channel(channel.chat_id, report_content)
                            reports_sent += 1
                            logger.info(f"Sent monthly performance report to channel {channel.name}")
                        except Exception as send_error:
                            logger.error(f"Failed to send monthly report to channel {channel.name}: {send_error}")
                    else:
                        logger.warning(f"Failed to generate report content for channel {channel.name}")
                        
                except Exception as e:
                    logger.error(f"Error generating monthly report for channel {channel.name}: {e}", exc_info=True)
                    continue
            
            return reports_sent
            
    except Exception as e:
        logger.error(f"Error generating monthly performance reports: {e}", exc_info=True)
        return 0

async def get_monthly_performance_stats(db, start_date_utc, end_date_utc) -> Dict[str, Any]:
    """
    Get performance statistics for the specified month
    """
    try:
        # Get all signals from the month
        signals_query = select(TradingSignal).where(
            and_(
                TradingSignal.created_at >= start_date_utc,
                TradingSignal.created_at <= end_date_utc
            )
        )
        
        if is_sqlite_db():
            signals_result = db.execute(signals_query)
        else:
            signals_result = await db.execute(signals_query)
        
        signals = signals_result.scalars().all()
        
        # Get TP hits for the month
        tp_hits_query = select(TakeProfitHit).where(
            and_(
                TakeProfitHit.hit_time >= start_date_utc,
                TakeProfitHit.hit_time <= end_date_utc
            )
        )
        
        if is_sqlite_db():
            tp_hits_result = db.execute(tp_hits_query)
        else:
            tp_hits_result = await db.execute(tp_hits_query)
        
        tp_hits = tp_hits_result.scalars().all()
        
        # Calculate statistics
        stats = {
            'total_signals': len(signals),
            'tp1_hits': len([hit for hit in tp_hits if hit.tp_level == 1]),
            'tp2_hits': len([hit for hit in tp_hits if hit.tp_level == 2]),
            'tp3_hits': len([hit for hit in tp_hits if hit.tp_level == 3]),
            'sl_hits': len([signal for signal in signals if signal.status == SignalStatus.SL_HIT]),
            'active_signals': len([signal for signal in signals if signal.status == SignalStatus.ACTIVE]),
            'symbols': list(set([signal.symbol for signal in signals])),
            'profitable_signals': len([signal for signal in signals if signal.profit_loss and signal.profit_loss > 0]),
            'losing_signals': len([signal for signal in signals if signal.profit_loss and signal.profit_loss < 0]),
        }
        
        # Calculate win rate based on TP hits vs SL hits
        total_tp_hits = stats['tp1_hits'] + stats['tp2_hits'] + stats['tp3_hits']
        total_completed = total_tp_hits + stats['sl_hits']
        stats['win_rate'] = (total_tp_hits / total_completed * 100) if total_completed > 0 else 0
        stats['total_tp_hits'] = total_tp_hits
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting monthly performance stats: {e}", exc_info=True)
        return {
            'total_signals': 0,
            'tp1_hits': 0,
            'tp2_hits': 0,
            'tp3_hits': 0,
            'sl_hits': 0,
            'active_signals': 0,
            'symbols': [],
            'profitable_signals': 0,
            'losing_signals': 0,
            'win_rate': 0
        }

async def generate_monthly_report_content(stats: Dict[str, Any], start_date, end_date, language: str, brand_name: str, qwen_client) -> str:
    """
    Generate monthly performance report content using AI
    """
    try:
        # Prepare statistics summary
        period_str = f"{start_date.strftime('%B %Y')}"

        # Create symbols summary (limit to avoid telegram message limits)
        symbols_text = ", ".join(stats['symbols'][:15])  # Limit to 15 symbols for monthly
        if len(stats['symbols']) > 15:
            symbols_text += f" and {len(stats['symbols']) - 15} more"

        # Prepare prompt for AI
        if language.lower() in ['fa', 'persian', 'farsi']:
            prompt = f"""Generate a SHORT Persian monthly summary for {brand_name}.

Period: {period_str}
Statistics:
- Total Signals: {stats['total_signals']}
- TP1: {stats['tp1_hits']}, TP2: {stats['tp2_hits']}, TP3: {stats['tp3_hits']}
- Stop Loss: {stats['sl_hits']}
- Win Rate: {stats['win_rate']:.0f}%
- Symbols: {symbols_text}

CRITICAL Requirements:
1. Write ONLY in Persian/Farsi
2. Maximum 300 characters
3. NO Chinese characters
4. Use trading terms: استاپ لاس (not توقف ضرر)
5. MUST include win rate: {stats['win_rate']:.0f}%
6. Use line breaks between sections
7. Simple format with emojis
8. Mention this is monthly summary

Generate Persian monthly summary only."""
        else:
            prompt = f"""Generate a SHORT English monthly summary for {brand_name}.

Period: {period_str}
Statistics:
- Total Signals: {stats['total_signals']}
- TP1: {stats['tp1_hits']}, TP2: {stats['tp2_hits']}, TP3: {stats['tp3_hits']}
- Stop Loss: {stats['sl_hits']}
- Win Rate: {stats['win_rate']:.0f}%
- Symbols: {symbols_text}

CRITICAL Requirements:
1. Write ONLY in English
2. Maximum 300 characters
3. NO Chinese characters
4. MUST include win rate: {stats['win_rate']:.0f}%
5. Use line breaks between sections
6. Simple format with emojis
7. Mention this is monthly summary

Generate English monthly summary only."""

        # Generate content using AI
        content = await qwen_client.generate_content(
            prompt=prompt,
            max_tokens=1000,
            temperature=0.7
        )

        if content and len(content.strip()) > 50:
            # Validate AI content quality
            content = content.strip()

            # Check for issues
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in content)
            has_win_rate = str(int(stats['win_rate'])) in content or f"{stats['win_rate']:.0f}" in content
            is_too_long = len(content) > 500

            # Use AI content only if it passes validation
            if not has_chinese and has_win_rate and not is_too_long:
                return content
            else:
                logger.warning(f"AI content failed validation: chinese={has_chinese}, win_rate={has_win_rate}, too_long={is_too_long}")
                # Fallback to simple format
                return generate_simple_monthly_report(stats, period_str, language, brand_name)
        else:
            # Fallback to simple format if AI fails
            return generate_simple_monthly_report(stats, period_str, language, brand_name)

    except Exception as e:
        logger.error(f"Error generating monthly report content: {e}", exc_info=True)
        # Fallback to simple format
        return generate_simple_monthly_report(stats, period_str, language, brand_name)

def generate_simple_monthly_report(stats: Dict[str, Any], period_str: str, language: str, brand_name: str) -> str:
    """
    Generate a simple monthly report as fallback
    """
    total_tp = stats.get('total_tp_hits', stats['tp1_hits'] + stats['tp2_hits'] + stats['tp3_hits'])

    if language.lower() in ['fa', 'persian', 'farsi']:
        # Persian/Farsi version
        report = f"""📊 گزارش ماهانه {brand_name}
📅 {period_str}

📈 نتایج ماه:
• کل سیگنال: {stats['total_signals']}
• TP موفق: {total_tp}
• استاپ لاس: {stats['sl_hits']}
• نرخ موفقیت: {stats['win_rate']:.0f}%
• نمادها: {len(stats['symbols'])} جفت ارز

🎯 ماه جدید، فرصت‌های جدید!"""
    else:
        # English version
        report = f"""📊 {brand_name} Monthly Summary
📅 {period_str}

📈 Month Results:
• Total Signals: {stats['total_signals']}
• TP Hits: {total_tp}
• SL Hits: {stats['sl_hits']}
• Win Rate: {stats['win_rate']:.0f}%
• Symbols: {len(stats['symbols'])} pairs

🎯 New month, new opportunities!"""

    return report

def get_next_monthly_report_time():
    """
    Get the next scheduled monthly report time

    Returns:
        datetime: Next monthly report time in configured timezone
    """
    now_local = get_current_time()

    # Calculate next first day of month at 11 AM
    if now_local.day == 1 and now_local.time() >= time(11, 0):
        # If it's the first day and past 11 AM, schedule for next month
        if now_local.month == 12:
            next_month = now_local.replace(year=now_local.year + 1, month=1, day=1)
        else:
            next_month = now_local.replace(month=now_local.month + 1, day=1)
    else:
        # Calculate first day of current month if we haven't passed 11 AM yet
        if now_local.day == 1 and now_local.time() < time(11, 0):
            next_month = now_local.replace(day=1)
        else:
            # Calculate first day of next month
            if now_local.month == 12:
                next_month = now_local.replace(year=now_local.year + 1, month=1, day=1)
            else:
                next_month = now_local.replace(month=now_local.month + 1, day=1)

    next_run = next_month.replace(hour=11, minute=0, second=0, microsecond=0)

    return next_run

def get_time_until_next_monthly_report():
    """
    Get time until next monthly report

    Returns:
        timedelta: Time until next monthly report
    """
    now_local = get_current_time()
    next_report = get_next_monthly_report_time()
    return next_report - now_local

async def start_monthly_performance_scheduler():
    """Start the monthly performance report scheduler"""
    logger.info("Starting monthly performance report scheduler")
    await monthly_performance_scheduler()
