"""
Real-time event scheduler for MignalyBot
Monitors events and sends notifications when they are released
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from sqlalchemy import select, and_

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import EconomicEvent, Channel
from src.ai_integration.qwen_client import <PERSON>wenClient
from src.telegram.bot import send_message_to_channel
from src.data_collection.economic_calendar import fetch_events_from_forex_factory

logger = logging.getLogger(__name__)

class EventScheduler:
    """Real-time event scheduler for economic events"""
    
    def __init__(self):
        self.qwen_client = QwenClient()
        self.running = False
        
    async def start(self):
        """Start the event scheduler"""
        self.running = True
        logger.info("Starting real-time event scheduler")
        
        while self.running:
            try:
                await self.check_and_notify_events()
                # Check every 30 seconds
                await asyncio.sleep(30)
            except Exception as e:
                logger.error(f"Error in event scheduler: {e}", exc_info=True)
                await asyncio.sleep(60)  # Wait longer on error
                
    async def stop(self):
        """Stop the event scheduler"""
        self.running = False
        logger.info("Stopping real-time event scheduler")
        
    async def check_and_notify_events(self):
        """Check for events that just happened and send notifications"""
        try:
            # Get current time
            now = datetime.now(timezone.utc)
            
            # Look for events that happened in the last 2 minutes
            start_time = now - timedelta(minutes=2)
            end_time = now + timedelta(minutes=1)  # Small buffer for timing
            
            # Get events from database that just happened
            recent_events = await self.get_recent_events(start_time, end_time)
            
            if recent_events:
                logger.info(f"Found {len(recent_events)} recent events to check")
                
                # Fetch latest data from Forex Factory to check for actual values
                latest_events = await fetch_events_from_forex_factory()
                
                # Match events and check for actual values
                for db_event in recent_events:
                    # Find matching event in latest data
                    matching_event = self.find_matching_event(db_event, latest_events)
                    
                    if matching_event and matching_event.get('actual'):
                        # Event has actual value, send notification
                        await self.send_event_notification(db_event, matching_event)
                        
        except Exception as e:
            logger.error(f"Error checking events: {e}", exc_info=True)
            
    async def get_recent_events(self, start_time, end_time):
        """Get events that happened recently"""
        events = []
        
        async for db in get_async_db():
            try:
                # Ensure timezone-aware datetimes for database query
                if start_time.tzinfo is None:
                    start_time_utc = start_time.replace(tzinfo=timezone.utc)
                else:
                    start_time_utc = start_time.astimezone(timezone.utc)

                if end_time.tzinfo is None:
                    end_time_utc = end_time.replace(tzinfo=timezone.utc)
                else:
                    end_time_utc = end_time.astimezone(timezone.utc)

                if is_sqlite_db():
                    result = db.execute(
                        select(EconomicEvent).where(
                            and_(
                                EconomicEvent.event_time >= start_time_utc,
                                EconomicEvent.event_time <= end_time_utc,
                                EconomicEvent.impact >= 2  # Medium to high impact only
                            )
                        ).order_by(EconomicEvent.event_time)
                    )
                else:
                    result = await db.execute(
                        select(EconomicEvent).where(
                            and_(
                                EconomicEvent.event_time >= start_time_utc,
                                EconomicEvent.event_time <= end_time_utc,
                                EconomicEvent.impact >= 2  # Medium to high impact only
                            )
                        ).order_by(EconomicEvent.event_time)
                    )
                
                events = result.scalars().all()
                break
                
            except Exception as e:
                logger.error(f"Error getting recent events: {e}")
                break
        
        return events
        
    def find_matching_event(self, db_event, latest_events):
        """Find matching event in latest data"""
        for event in latest_events:
            if (event['title'] == db_event.title and 
                event.get('country') == db_event.country):
                return event
        return None
        
    async def send_event_notification(self, db_event, latest_event):
        """Send notification for event with actual value"""
        try:
            logger.info(f"Sending notification for event: {db_event.title}")
            
            # Get all active channels
            channels = await self.get_active_channels()
            
            for channel in channels:
                try:
                    # Generate AI analysis for the event result
                    content = await self.qwen_client.analyze_event_result(
                        db_event,
                        latest_event,
                        language=channel.language,
                        channel_brand=channel.brand_name
                    )
                    
                    if content:
                        # Send message to channel
                        await send_message_to_channel(channel.chat_id, content)
                        logger.info(f"Sent event notification to channel {channel.name}")
                    
                except Exception as e:
                    logger.error(f"Error sending notification to channel {channel.name}: {e}")
                    
        except Exception as e:
            logger.error(f"Error sending event notification: {e}", exc_info=True)
            
    async def get_active_channels(self):
        """Get all active channels"""
        channels = []
        
        async for db in get_async_db():
            try:
                if is_sqlite_db():
                    result = db.execute(
                        select(Channel).where(Channel.active == True)
                    )
                else:
                    result = await db.execute(
                        select(Channel).where(Channel.active == True)
                    )
                
                channels = result.scalars().all()
                break
                
            except Exception as e:
                logger.error(f"Error getting active channels: {e}")
                break
        
        return channels

# Global scheduler instance
event_scheduler = EventScheduler()

async def start_event_scheduler():
    """Start the global event scheduler"""
    await event_scheduler.start()
    
async def stop_event_scheduler():
    """Stop the global event scheduler"""
    await event_scheduler.stop()
