"""
Data collection scheduler for MignalyBot
"""

import asyncio
import logging
from datetime import datetime, timedelta, time
from sqlalchemy import select

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import Config
from src.data_collection.market_data import collect_market_data
from src.data_collection.news import collect_news
from src.data_collection.economic_calendar import collect_economic_events
from src.strategies.processor import process_strategies
from src.ai_integration.event_notifier import check_event_notifications
from src.ai_integration.content_generator import generate_content
from src.utils.helpers import get_current_time

logger = logging.getLogger(__name__)

def is_quiet_period():
    """
    Check if current time is in the quiet period (midnight to 9 AM)
    During this time, no data collection, post generation, or scheduling should occur

    Returns:
        bool: True if in quiet period, False otherwise
    """
    now_local = get_current_time()
    current_time = now_local.time()

    # Quiet period is from midnight (00:00) to 9 AM (09:00)
    quiet_start = time(0, 0)  # Midnight
    quiet_end = time(9, 0)    # 9 AM

    return quiet_start <= current_time < quiet_end

def get_next_allowed_run_time():
    """
    Get the next allowed run time, considering the quiet period

    Returns:
        datetime: Next allowed run time in local timezone
    """
    now_local = get_current_time()

    # If we're in quiet period, next run should be at 9 AM
    if is_quiet_period():
        next_run = now_local.replace(hour=9, minute=0, second=0, microsecond=0)
        # If it's already past 9 AM today (shouldn't happen in quiet period), schedule for tomorrow
        if now_local.time() >= time(9, 0):
            next_run += timedelta(days=1)
        return next_run

    # If not in quiet period, schedule for 6 hours from now
    # But make sure it doesn't fall in the next quiet period
    next_run = now_local + timedelta(hours=6)

    # If the next run would be in quiet period, schedule it for 9 AM instead
    if time(0, 0) <= next_run.time() < time(9, 0):
        next_run = next_run.replace(hour=9, minute=0, second=0, microsecond=0)

    return next_run

async def data_collection_task():
    """Main data collection task that runs periodically"""
    try:
        # Check if we're in the quiet period (midnight to 9 AM)
        if is_quiet_period():
            now_local = get_current_time()
            logger.info(f"🔇 Skipping data collection task - in quiet period (midnight to 9 AM). Current time: {now_local.strftime('%H:%M:%S %Z')}")
            return

        logger.info("🚀 Starting comprehensive data collection task")

        # Get configuration
        async for db in get_async_db():
            logger.debug("📋 Loading configuration from database...")

            # Handle SQLite differently than other databases
            if is_sqlite_db():
                config_result = db.execute(select(Config))
            else:
                config_result = await db.execute(select(Config))

            config = config_result.scalars().first()

            if not config:
                logger.error("❌ No configuration found in database")
                return

            symbols = [s.strip() for s in config.symbols.split(",")]
            timeframes = [t.strip() for t in config.timeframes.split(",")]

            logger.info(f"📊 Configuration loaded - Symbols: {symbols}, Timeframes: {timeframes}")
            logger.info(f"🔧 Features enabled - News: {config.enable_news}, Calendar: {config.enable_calendar}, Signals: {config.enable_signals}")

            # Collect market data
            logger.info("📈 Starting market data collection...")
            await collect_market_data(symbols, timeframes)
            logger.info("✅ Market data collection completed")

            # Collect news if enabled
            if config.enable_news:
                logger.info("📰 Starting news collection...")
                await collect_news(symbols)
                logger.info("✅ News collection completed")
            else:
                logger.info("⏭️ News collection disabled")

            # Collect economic calendar events if enabled
            if config.enable_calendar:
                logger.info("📅 Starting economic calendar collection...")
                await collect_economic_events()
                logger.info("✅ Economic calendar collection completed")
            else:
                logger.info("⏭️ Economic calendar collection disabled")

            # Process strategies if enabled
            if config.enable_signals:
                logger.info("🎯 Starting strategy processing...")
                await process_strategies()
                logger.info("✅ Strategy processing completed")
            else:
                logger.info("⏭️ Strategy processing disabled")

            # Check for event notifications (run every time)
            logger.info("🔔 Checking event notifications...")
            await check_event_notifications()
            logger.info("✅ Event notifications check completed")

            # Generate content for all channels (all post types including greeting)
            logger.info("🤖 Starting content generation...")
            posts_created = await generate_content()
            logger.info(f"✅ Content generation completed. Created {posts_created} posts")

            logger.info("🎉 Data collection task completed successfully")

    except Exception as e:
        logger.error(f"💥 Error in data collection task: {e}", exc_info=True)

async def start_data_collection():
    """Start the data collection scheduler - runs every 6 hours, respecting quiet period"""
    logger.info("Starting data collection scheduler (6-hour intervals, respecting quiet period midnight-9AM)")

    while True:
        try:
            # Run data collection task
            await data_collection_task()

            # Calculate next allowed run time
            next_run = get_next_allowed_run_time()
            now_local = get_current_time()
            time_until_run = (next_run - now_local).total_seconds()

            logger.info(f"Next data collection scheduled for: {next_run.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            logger.info(f"Time until next run: {time_until_run / 3600:.2f} hours")

            # Wait until the scheduled time
            await asyncio.sleep(time_until_run)

        except Exception as e:
            logger.error(f"Error in data collection scheduler: {e}", exc_info=True)
            # Wait 30 minutes before retrying on error
            await asyncio.sleep(1800)
