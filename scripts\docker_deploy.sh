#!/bin/bash

# MignalyBot Docker Deployment Script
# This script handles Docker-based deployment of MignalyBot

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed and running
check_docker() {
    log_info "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    log_success "Docker is installed and running"
}

# Check if .env file exists
check_env_file() {
    log_info "Checking environment configuration..."
    
    if [ ! -f ".env" ]; then
        log_error ".env file not found. Please create .env file with required variables."
        log_info "Required variables:"
        echo "  - DATABASE_URL"
        echo "  - TELEGRAM_BOT_TOKEN"
        echo "  - QWEN_API_KEY"
        echo "  - QWEN_API_URL"
        echo "  - TIMEZONE"
        exit 1
    fi
    
    log_success ".env file found"
}

# Build Docker image
build_image() {
    log_info "Building MignalyBot Docker image..."
    
    if docker build -t mignalybot:latest .; then
        log_success "Docker image built successfully"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
}

# Stop existing container if running
stop_existing() {
    log_info "Checking for existing MignalyBot container..."
    
    if docker ps -q -f name=mignalybot | grep -q .; then
        log_info "Stopping existing MignalyBot container..."
        docker stop mignalybot
        docker rm mignalybot
        log_success "Existing container stopped and removed"
    else
        log_info "No existing container found"
    fi
}

# Run database migrations and setup
run_migrations() {
    log_info "Running database migrations and setup..."
    
    # Run the deployment script inside a temporary container
    if docker run --rm \
        --env-file .env \
        -v "$(pwd)/logs:/app/logs" \
        mignalybot:latest \
        python scripts/deploy_mignalybot.py; then
        log_success "Database migrations and setup completed"
    else
        log_error "Database migrations failed"
        exit 1
    fi
}

# Start the application container
start_container() {
    log_info "Starting MignalyBot container..."
    
    if docker run -d \
        --name mignalybot \
        --restart unless-stopped \
        -p 9000:8000 \
        --env-file .env \
        -v "$(pwd)/logs:/app/logs" \
        -v "$(pwd)/data:/app/data" \
        mignalybot:latest; then
        log_success "MignalyBot container started successfully"
    else
        log_error "Failed to start MignalyBot container"
        exit 1
    fi
}

# Wait for application to be ready
wait_for_app() {
    log_info "Waiting for application to be ready..."
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:9000/health &> /dev/null; then
            log_success "Application is ready!"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts - waiting for application..."
        sleep 2
        ((attempt++))
    done
    
    log_warning "Application may not be fully ready yet. Check logs with: docker logs mignalybot"
}

# Show deployment status
show_status() {
    log_info "Deployment Status:"
    echo "===================="
    
    # Container status
    if docker ps -f name=mignalybot --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep mignalybot; then
        log_success "Container is running"
    else
        log_error "Container is not running"
    fi
    
    echo ""
    log_info "Access Information:"
    echo "  - Admin Interface: http://localhost:9000"
    echo "  - Default Login: admin / admin123"
    echo "  - Logs: docker logs mignalybot"
    echo "  - Stop: docker stop mignalybot"
    echo "  - Restart: docker restart mignalybot"
}

# Main deployment function
main() {
    echo "🚀 MignalyBot Docker Deployment"
    echo "================================"
    
    # Pre-deployment checks
    check_docker
    check_env_file
    
    # Build and deploy
    build_image
    stop_existing
    run_migrations
    start_container
    wait_for_app
    
    echo ""
    echo "🎉 Deployment completed successfully!"
    echo ""
    show_status
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        log_info "Stopping MignalyBot..."
        docker stop mignalybot || true
        docker rm mignalybot || true
        log_success "MignalyBot stopped"
        ;;
    "restart")
        log_info "Restarting MignalyBot..."
        docker restart mignalybot
        log_success "MignalyBot restarted"
        ;;
    "logs")
        log_info "Showing MignalyBot logs..."
        docker logs -f mignalybot
        ;;
    "status")
        show_status
        ;;
    "rebuild")
        log_info "Rebuilding and redeploying MignalyBot..."
        docker stop mignalybot || true
        docker rm mignalybot || true
        main
        ;;
    *)
        echo "Usage: $0 {deploy|stop|restart|logs|status|rebuild}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Deploy MignalyBot (default)"
        echo "  stop     - Stop MignalyBot container"
        echo "  restart  - Restart MignalyBot container"
        echo "  logs     - Show MignalyBot logs"
        echo "  status   - Show deployment status"
        echo "  rebuild  - Rebuild and redeploy MignalyBot"
        exit 1
        ;;
esac
